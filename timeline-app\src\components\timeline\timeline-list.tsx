'use client'

import React, { useState } from 'react'
import { Plus, Calendar, Edit, Trash2, <PERSON><PERSON><PERSON>, <PERSON>, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useTimelines, useCreateTimeline, useDeleteTimeline } from '@/hooks/use-timelines'
import { Timeline } from '@/types/timeline'

interface TimelineListProps {
  onTimelineSelect: (timeline: Timeline) => void
}

export const TimelineList: React.FC<TimelineListProps> = ({ onTimelineSelect }) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newTimelineTitle, setNewTimelineTitle] = useState('')
  const [newTimelineDescription, setNewTimelineDescription] = useState('')

  const { data: timelines, isLoading, error } = useTimelines()
  const createTimelineMutation = useCreateTimeline()
  const deleteTimelineMutation = useDeleteTimeline()

  const handleCreateTimeline = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newTimelineTitle.trim()) return

    try {
      const newTimeline = await createTimelineMutation.mutateAsync({
        title: newTimelineTitle.trim(),
        description: newTimelineDescription.trim() || undefined,
      })

      setNewTimelineTitle('')
      setNewTimelineDescription('')
      setIsCreateDialogOpen(false)
      onTimelineSelect(newTimeline)
    } catch (error) {
      console.error('Failed to create timeline:', error)
    }
  }

  const handleDeleteTimeline = async (timelineId: string) => {
    if (!confirm('Are you sure you want to delete this timeline? This action cannot be undone.')) {
      return
    }

    try {
      await deleteTimelineMutation.mutateAsync(timelineId)
    } catch (error) {
      console.error('Failed to delete timeline:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading timelines...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Failed to load timelines</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">My Timelines</h1>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Timeline
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Timeline</DialogTitle>
              <DialogDescription>
                {!timelines || timelines.length === 0
                  ? "Let's create your first timeline! Give it a meaningful title and description to get started."
                  : "Create a new timeline to organize your events chronologically."
                }
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateTimeline} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newTimelineTitle}
                  onChange={(e) => setNewTimelineTitle(e.target.value)}
                  placeholder={!timelines || timelines.length === 0
                    ? "e.g., My Life Journey, Project Milestones, Company History"
                    : "Enter timeline title"
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (optional)</Label>
                <Textarea
                  id="description"
                  value={newTimelineDescription}
                  onChange={(e) => setNewTimelineDescription(e.target.value)}
                  placeholder={!timelines || timelines.length === 0
                    ? "Describe what this timeline will track - key events, milestones, or memories you want to organize"
                    : "Enter timeline description"
                  }
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createTimelineMutation.isPending}
                >
                  {createTimelineMutation.isPending
                    ? 'Creating...'
                    : (!timelines || timelines.length === 0
                        ? 'Create My First Timeline'
                        : 'Create Timeline'
                      )
                  }
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {!timelines || timelines.length === 0 ? (
        <div className="space-y-8">
          {/* Welcome Header */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Calendar className="w-16 h-16 text-blue-500" />
                <Sparkles className="w-6 h-6 text-yellow-500 absolute -top-1 -right-1" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Timeline App!</h2>
            <p className="text-gray-600 max-w-md mx-auto">
              Create your first timeline to start organizing events, milestones, and memories in a beautiful visual format.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
            <Card className="text-center p-4">
              <CardContent className="pt-4">
                <Clock className="w-8 h-8 text-blue-500 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Chronological Order</h3>
                <p className="text-sm text-gray-600">
                  Events are automatically organized by date for clear storytelling.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-4">
              <CardContent className="pt-4">
                <Edit className="w-8 h-8 text-green-500 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Interactive Canvas</h3>
                <p className="text-sm text-gray-600">
                  Click anywhere to add events and drag them to perfect positions.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-4">
              <CardContent className="pt-4">
                <Users className="w-8 h-8 text-purple-500 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Personal & Shareable</h3>
                <p className="text-sm text-gray-600">
                  Create timelines for projects, life events, or historical research.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Call to Action */}
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Ready to get started?</h3>
              <p className="text-gray-600 mb-6">
                Create your first timeline and start adding events to see the magic happen.
              </p>
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                size="lg"
                className="w-full"
              >
                <Plus className="w-5 h-5 mr-2" />
                Create Your First Timeline
              </Button>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {timelines.map((timeline) => (
            <Card key={timeline.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1" onClick={() => onTimelineSelect(timeline)}>
                    <CardTitle className="text-lg">{timeline.title}</CardTitle>
                    {timeline.description && (
                      <CardDescription className="mt-2">
                        {timeline.description}
                      </CardDescription>
                    )}
                  </div>

                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteTimeline(timeline.id)
                      }}
                      disabled={deleteTimelineMutation.isPending}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent onClick={() => onTimelineSelect(timeline)}>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>
                    Updated {new Date(timeline.updated_at).toLocaleDateString()}
                  </span>
                  <Badge variant="secondary">Timeline</Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
