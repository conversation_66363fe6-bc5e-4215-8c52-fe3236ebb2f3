import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { TimelineItem, CreateTimelineItemData, UpdateTimelineItemData } from '@/types/timeline'

// Mock data storage
const getMockTimelineItems = (): TimelineItem[] => {
  const stored = localStorage.getItem('mock-timeline-items')
  return stored ? JSON.parse(stored) : []
}

const setMockTimelineItems = (items: TimelineItem[]) => {
  localStorage.setItem('mock-timeline-items', JSON.stringify(items))
}

// Fetch all items for a specific timeline
export const useTimelineItems = (timelineId: string) => {
  return useQuery({
    queryKey: ['timeline-items', timelineId],
    queryFn: async (): Promise<TimelineItem[]> => {
      if (!supabase) {
        // Mock data for development
        const allItems = getMockTimelineItems()
        return allItems.filter(item => item.timeline_id === timelineId)
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      }

      const { data, error } = await supabase
        .from('timeline_items')
        .select('*')
        .eq('timeline_id', timelineId)
        .order('date', { ascending: true })

      if (error) throw error
      return data || []
    },
    enabled: !!timelineId,
  })
}

// Create a new timeline item
export const useCreateTimelineItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (itemData: CreateTimelineItemData): Promise<TimelineItem> => {
      if (!supabase) {
        // Mock data for development
        const newItem: TimelineItem = {
          id: `item-${Date.now()}`,
          ...itemData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }

        const items = getMockTimelineItems()
        items.push(newItem)
        setMockTimelineItems(items)
        return newItem
      }

      const { data, error } = await supabase
        .from('timeline_items')
        .insert(itemData)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })
    },
  })
}

// Update a timeline item
export const useUpdateTimelineItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineItemData }): Promise<TimelineItem> => {
      if (!supabase) {
        // Mock data for development
        const items = getMockTimelineItems()
        const itemIndex = items.findIndex(item => item.id === id)
        if (itemIndex === -1) throw new Error('Timeline item not found')

        const updatedItem = {
          ...items[itemIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        }

        items[itemIndex] = updatedItem
        setMockTimelineItems(items)
        return updatedItem
      }

      const { data, error } = await supabase
        .from('timeline_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })
    },
  })
}

// Delete a timeline item
export const useDeleteTimelineItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (id: string): Promise<{ timeline_id: string }> => {
      if (!supabase) {
        // Mock data for development
        const items = getMockTimelineItems()
        const item = items.find(item => item.id === id)
        if (!item) throw new Error('Timeline item not found')

        const filteredItems = items.filter(item => item.id !== id)
        setMockTimelineItems(filteredItems)
        return { timeline_id: item.timeline_id }
      }

      // First get the timeline_id before deleting
      const { data: item, error: fetchError } = await supabase
        .from('timeline_items')
        .select('timeline_id')
        .eq('id', id)
        .single()

      if (fetchError) throw fetchError

      const { error } = await supabase
        .from('timeline_items')
        .delete()
        .eq('id', id)

      if (error) throw error
      return { timeline_id: item.timeline_id }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })
    },
  })
}
