{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      timelines: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          user_id: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          user_id: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          user_id?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      timeline_items: {\n        Row: {\n          id: string\n          timeline_id: string\n          title: string\n          description: string | null\n          date: string\n          x_position: number\n          y_position: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          timeline_id: string\n          title: string\n          description?: string | null\n          date: string\n          x_position: number\n          y_position: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          timeline_id?: string\n          title?: string\n          description?: string | null\n          date?: string\n          x_position?: number\n          y_position?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,QAAQ,GAAG,CAAC,6BAA6B;AAE1D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-auth.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\nexport const useAuth = () => {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { data, error }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n    return { data, error }\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    return { error }\n  }\n\n  const resetPassword = async (email: string) => {\n    const { data, error } = await supabase.auth.resetPasswordForEmail(email)\n    return { data, error }\n  }\n\n  return {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,MAAM,UAAU;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAClE,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/auth/auth-form.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuth } from '@/hooks/use-auth'\n\ninterface AuthFormProps {\n  mode: 'signin' | 'signup'\n  onModeChange: (mode: 'signin' | 'signup') => void\n}\n\nexport const AuthForm: React.FC<AuthFormProps> = ({ mode, onModeChange }) => {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [message, setMessage] = useState<string | null>(null)\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(null)\n    setMessage(null)\n\n    try {\n      if (mode === 'signin') {\n        const { error } = await signIn(email, password)\n        if (error) throw error\n      } else {\n        const { error } = await signUp(email, password)\n        if (error) throw error\n        setMessage('Check your email for the confirmation link!')\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader>\n        <CardTitle>\n          {mode === 'signin' ? 'Sign In' : 'Sign Up'}\n        </CardTitle>\n        <CardDescription>\n          {mode === 'signin' \n            ? 'Enter your credentials to access your timelines'\n            : 'Create an account to start building timelines'\n          }\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              disabled={loading}\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\">Password</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              disabled={loading}\n              minLength={6}\n            />\n          </div>\n\n          {error && (\n            <div className=\"text-sm text-red-600 bg-red-50 p-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          {message && (\n            <div className=\"text-sm text-green-600 bg-green-50 p-3 rounded-md\">\n              {message}\n            </div>\n          )}\n\n          <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n            {loading ? 'Loading...' : (mode === 'signin' ? 'Sign In' : 'Sign Up')}\n          </Button>\n        </form>\n\n        <div className=\"mt-4 text-center\">\n          <button\n            type=\"button\"\n            onClick={() => onModeChange(mode === 'signin' ? 'signup' : 'signin')}\n            className=\"text-sm text-blue-600 hover:text-blue-800 underline\"\n            disabled={loading}\n          >\n            {mode === 'signin' \n              ? \"Don't have an account? Sign up\"\n              : 'Already have an account? Sign in'\n            }\n          </button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAcO,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO,MAAM;YACnB,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO,MAAM;gBACjB,WAAW;YACb;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCACP,SAAS,WAAW,YAAY;;;;;;kCAEnC,8OAAC,gIAAA,CAAA,kBAAe;kCACb,SAAS,WACN,oDACA;;;;;;;;;;;;0BAIR,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,QAAQ;wCACR,UAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ;wCACR,UAAU;wCACV,WAAW;;;;;;;;;;;;4BAId,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;4BAIJ,yBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;gCAAS,UAAU;0CAChD,UAAU,eAAgB,SAAS,WAAW,YAAY;;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa,SAAS,WAAW,WAAW;4BAC3D,WAAU;4BACV,UAAU;sCAET,SAAS,WACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-timelines.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabase } from '@/lib/supabase'\nimport { Timeline, CreateTimelineData, UpdateTimelineData } from '@/types/timeline'\n\n// Fetch all timelines for the current user\nexport const useTimelines = () => {\n  return useQuery({\n    queryKey: ['timelines'],\n    queryFn: async (): Promise<Timeline[]> => {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .select('*')\n        .eq('user_id', user.id)\n        .order('updated_at', { ascending: false })\n\n      if (error) throw error\n      return data || []\n    },\n  })\n}\n\n// Fetch a single timeline by ID\nexport const useTimeline = (id: string) => {\n  return useQuery({\n    queryKey: ['timeline', id],\n    queryFn: async (): Promise<Timeline> => {\n      const { data, error } = await supabase\n        .from('timelines')\n        .select('*')\n        .eq('id', id)\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    enabled: !!id,\n  })\n}\n\n// Create a new timeline\nexport const useCreateTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (timelineData: CreateTimelineData): Promise<Timeline> => {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .insert({\n          ...timelineData,\n          user_id: user.id,\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n    },\n  })\n}\n\n// Update a timeline\nexport const useUpdateTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineData }): Promise<Timeline> => {\n      const { data, error } = await supabase\n        .from('timelines')\n        .update(updates)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n      queryClient.invalidateQueries({ queryKey: ['timeline', data.id] })\n    },\n  })\n}\n\n// Delete a timeline\nexport const useDeleteTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (id: string): Promise<void> => {\n      const { error } = await supabase\n        .from('timelines')\n        .delete()\n        .eq('id', id)\n\n      if (error) throw error\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;AAIO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAY;QACvB,SAAS;YACP,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB;IACF;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;gBACN,GAAG,YAAY;gBACf,SAAS,KAAK,EAAE;YAClB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;QAC1D;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,OAAO,EAA+C;YAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,KAAK,EAAE;iBAAC;YAAC;QAClE;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;QAC1D;IACF;AACF", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/timeline/timeline-list.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Plus, Calendar, Edit, Trash2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { useTimelines, useCreateTimeline, useDeleteTimeline } from '@/hooks/use-timelines'\nimport { Timeline } from '@/types/timeline'\n\ninterface TimelineListProps {\n  onTimelineSelect: (timeline: Timeline) => void\n}\n\nexport const TimelineList: React.FC<TimelineListProps> = ({ onTimelineSelect }) => {\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\n  const [newTimelineTitle, setNewTimelineTitle] = useState('')\n  const [newTimelineDescription, setNewTimelineDescription] = useState('')\n\n  const { data: timelines, isLoading, error } = useTimelines()\n  const createTimelineMutation = useCreateTimeline()\n  const deleteTimelineMutation = useDeleteTimeline()\n\n  const handleCreateTimeline = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!newTimelineTitle.trim()) return\n\n    try {\n      const newTimeline = await createTimelineMutation.mutateAsync({\n        title: newTimelineTitle.trim(),\n        description: newTimelineDescription.trim() || undefined,\n      })\n      \n      setNewTimelineTitle('')\n      setNewTimelineDescription('')\n      setIsCreateDialogOpen(false)\n      onTimelineSelect(newTimeline)\n    } catch (error) {\n      console.error('Failed to create timeline:', error)\n    }\n  }\n\n  const handleDeleteTimeline = async (timelineId: string) => {\n    if (!confirm('Are you sure you want to delete this timeline? This action cannot be undone.')) {\n      return\n    }\n\n    try {\n      await deleteTimelineMutation.mutateAsync(timelineId)\n    } catch (error) {\n      console.error('Failed to delete timeline:', error)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">Loading timelines...</div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-red-500\">Failed to load timelines</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">My Timelines</h1>\n        \n        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>\n              <Plus className=\"w-4 h-4 mr-2\" />\n              New Timeline\n            </Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Create New Timeline</DialogTitle>\n              <DialogDescription>\n                Create a new timeline to organize your events chronologically.\n              </DialogDescription>\n            </DialogHeader>\n            <form onSubmit={handleCreateTimeline} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"title\">Title</Label>\n                <Input\n                  id=\"title\"\n                  value={newTimelineTitle}\n                  onChange={(e) => setNewTimelineTitle(e.target.value)}\n                  placeholder=\"Enter timeline title\"\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">Description (optional)</Label>\n                <Textarea\n                  id=\"description\"\n                  value={newTimelineDescription}\n                  onChange={(e) => setNewTimelineDescription(e.target.value)}\n                  placeholder=\"Enter timeline description\"\n                  rows={3}\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsCreateDialogOpen(false)}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  disabled={createTimelineMutation.isPending}\n                >\n                  {createTimelineMutation.isPending ? 'Creating...' : 'Create Timeline'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {!timelines || timelines.length === 0 ? (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-12\">\n            <Calendar className=\"w-12 h-12 text-gray-400 mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No timelines yet</h3>\n            <p className=\"text-gray-500 text-center mb-4\">\n              Create your first timeline to start organizing events chronologically.\n            </p>\n            <Button onClick={() => setIsCreateDialogOpen(true)}>\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Create Timeline\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {timelines.map((timeline) => (\n            <Card key={timeline.id} className=\"hover:shadow-md transition-shadow cursor-pointer\">\n              <CardHeader>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\" onClick={() => onTimelineSelect(timeline)}>\n                    <CardTitle className=\"text-lg\">{timeline.title}</CardTitle>\n                    {timeline.description && (\n                      <CardDescription className=\"mt-2\">\n                        {timeline.description}\n                      </CardDescription>\n                    )}\n                  </div>\n                  \n                  <div className=\"flex space-x-1\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteTimeline(timeline.id)\n                      }}\n                      disabled={deleteTimelineMutation.isPending}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardHeader>\n              \n              <CardContent onClick={() => onTimelineSelect(timeline)}>\n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                  <span>\n                    Updated {new Date(timeline.updated_at).toLocaleDateString()}\n                  </span>\n                  <Badge variant=\"secondary\">Timeline</Badge>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAkBO,MAAM,eAA4C,CAAC,EAAE,gBAAgB,EAAE;IAC5E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,EAAE,MAAM,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IACzD,MAAM,yBAAyB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAC/C,MAAM,yBAAyB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAE/C,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAEhB,IAAI,CAAC,iBAAiB,IAAI,IAAI;QAE9B,IAAI;YACF,MAAM,cAAc,MAAM,uBAAuB,WAAW,CAAC;gBAC3D,OAAO,iBAAiB,IAAI;gBAC5B,aAAa,uBAAuB,IAAI,MAAM;YAChD;YAEA,oBAAoB;YACpB,0BAA0B;YAC1B,sBAAsB;YACtB,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,iFAAiF;YAC5F;QACF;QAEA,IAAI;YACF,MAAM,uBAAuB,WAAW,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAe;;;;;;;;;;;IAGpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAEjD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAoB,cAAc;;0CAC9C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,8OAAC;wCAAK,UAAU;wCAAsB,WAAU;;0DAC9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;wDACzD,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,sBAAsB;kEACtC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU,uBAAuB,SAAS;kEAEzC,uBAAuB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,CAAC,aAAa,UAAU,MAAM,KAAK,kBAClC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,sBAAsB;;8CAC3C,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAMvC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAS,SAAS,IAAM,iBAAiB;;8DACtD,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,KAAK;;;;;;gDAC7C,SAAS,WAAW,kBACnB,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,SAAS,WAAW;;;;;;;;;;;;sDAK3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,qBAAqB,SAAS,EAAE;gDAClC;gDACA,UAAU,uBAAuB,SAAS;0DAE1C,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,SAAS,IAAM,iBAAiB;0CAC3C,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDACK,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;sDAE3D,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;;;;;;;;;;;;uBAjCtB,SAAS,EAAE;;;;;;;;;;;;;;;;AA0ClC", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/stores/timeline-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { Timeline, TimelineItem, CanvasViewport } from '@/types/timeline'\n\ninterface TimelineStore {\n  // Current timeline data\n  currentTimeline: Timeline | null\n  timelineItems: TimelineItem[]\n  \n  // Canvas state\n  viewport: CanvasViewport\n  selectedItemId: string | null\n  isDragging: boolean\n  \n  // UI state\n  isLoading: boolean\n  error: string | null\n  \n  // Actions\n  setCurrentTimeline: (timeline: Timeline | null) => void\n  setTimelineItems: (items: TimelineItem[]) => void\n  addTimelineItem: (item: TimelineItem) => void\n  updateTimelineItem: (id: string, updates: Partial<TimelineItem>) => void\n  removeTimelineItem: (id: string) => void\n  \n  // Canvas actions\n  setViewport: (viewport: CanvasViewport) => void\n  setSelectedItemId: (id: string | null) => void\n  setIsDragging: (isDragging: boolean) => void\n  \n  // UI actions\n  setIsLoading: (isLoading: boolean) => void\n  setError: (error: string | null) => void\n  \n  // Reset store\n  reset: () => void\n}\n\nconst initialState = {\n  currentTimeline: null,\n  timelineItems: [],\n  viewport: { x: 0, y: 0, scale: 1 },\n  selectedItemId: null,\n  isDragging: false,\n  isLoading: false,\n  error: null,\n}\n\nexport const useTimelineStore = create<TimelineStore>((set, get) => ({\n  ...initialState,\n  \n  setCurrentTimeline: (timeline) => set({ currentTimeline: timeline }),\n  \n  setTimelineItems: (items) => set({ timelineItems: items }),\n  \n  addTimelineItem: (item) => set((state) => ({\n    timelineItems: [...state.timelineItems, item]\n  })),\n  \n  updateTimelineItem: (id, updates) => set((state) => ({\n    timelineItems: state.timelineItems.map(item =>\n      item.id === id ? { ...item, ...updates } : item\n    )\n  })),\n  \n  removeTimelineItem: (id) => set((state) => ({\n    timelineItems: state.timelineItems.filter(item => item.id !== id)\n  })),\n  \n  setViewport: (viewport) => set({ viewport }),\n  \n  setSelectedItemId: (id) => set({ selectedItemId: id }),\n  \n  setIsDragging: (isDragging) => set({ isDragging }),\n  \n  setIsLoading: (isLoading) => set({ isLoading }),\n  \n  setError: (error) => set({ error }),\n  \n  reset: () => set(initialState),\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,eAAe;IACnB,iBAAiB;IACjB,eAAe,EAAE;IACjB,UAAU;QAAE,GAAG;QAAG,GAAG;QAAG,OAAO;IAAE;IACjC,gBAAgB;IAChB,YAAY;IACZ,WAAW;IACX,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACnE,GAAG,YAAY;QAEf,oBAAoB,CAAC,WAAa,IAAI;gBAAE,iBAAiB;YAAS;QAElE,kBAAkB,CAAC,QAAU,IAAI;gBAAE,eAAe;YAAM;QAExD,iBAAiB,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACzC,eAAe;2BAAI,MAAM,aAAa;wBAAE;qBAAK;gBAC/C,CAAC;QAED,oBAAoB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACnD,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,OACrC,KAAK,EAAE,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE/C,CAAC;QAED,oBAAoB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC1C,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAChE,CAAC;QAED,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,mBAAmB,CAAC,KAAO,IAAI;gBAAE,gBAAgB;YAAG;QAEpD,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAEhD,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE7C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,OAAO,IAAM,IAAI;IACnB,CAAC", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/timeline/timeline-canvas.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useRef, useEffect, useState } from 'react'\nimport dynamic from 'next/dynamic'\nimport { useTimelineStore } from '@/stores/timeline-store'\nimport { TimelineItem } from '@/types/timeline'\n\n// Create a wrapper component for the canvas\nconst TimelineCanvasInner = dynamic(() => import('./timeline-canvas-inner'), {\n  ssr: false,\n  loading: () => <div className=\"w-full h-full bg-gray-50 flex items-center justify-center\">Loading canvas...</div>\n})\n\ninterface TimelineCanvasProps {\n  timelineItems: TimelineItem[]\n  onItemClick?: (item: TimelineItem) => void\n  onItemMove?: (itemId: string, x: number, y: number) => void\n  onCanvasClick?: (x: number, y: number) => void\n}\n\nexport const TimelineCanvas: React.FC<TimelineCanvasProps> = ({\n  timelineItems,\n  onItemClick,\n  onItemMove,\n  onCanvasClick,\n}) => {\n  const stageRef = useRef<any>(null)\n  const [stageSize, setStageSize] = useState({ width: 800, height: 600 })\n\n  const {\n    viewport,\n    selectedItemId,\n    isDragging,\n    setViewport,\n    setSelectedItemId,\n    setIsDragging,\n  } = useTimelineStore()\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (stageRef.current) {\n        const container = stageRef.current.container().parentElement\n        setStageSize({\n          width: container.offsetWidth,\n          height: container.offsetHeight,\n        })\n      }\n    }\n\n    handleResize()\n    window.addEventListener('resize', handleResize)\n    return () => window.removeEventListener('resize', handleResize)\n  }, [])\n\n  // Handle wheel zoom\n  const handleWheel = (e: KonvaEventObject<WheelEvent>) => {\n    e.evt.preventDefault()\n\n    const stage = e.target.getStage()\n    if (!stage) return\n\n    const oldScale = viewport.scale\n    const pointer = stage.getPointerPosition()\n    if (!pointer) return\n\n    const mousePointTo = {\n      x: (pointer.x - viewport.x) / oldScale,\n      y: (pointer.y - viewport.y) / oldScale,\n    }\n\n    const direction = e.evt.deltaY > 0 ? -1 : 1\n    const scaleBy = 1.1\n    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy\n\n    // Limit zoom levels\n    const clampedScale = Math.max(0.1, Math.min(5, newScale))\n\n    const newPos = {\n      x: pointer.x - mousePointTo.x * clampedScale,\n      y: pointer.y - mousePointTo.y * clampedScale,\n    }\n\n    setViewport({\n      x: newPos.x,\n      y: newPos.y,\n      scale: clampedScale,\n    })\n  }\n\n  // Handle stage drag\n  const handleStageDragEnd = (e: KonvaEventObject<DragEvent>) => {\n    setViewport({\n      ...viewport,\n      x: e.target.x(),\n      y: e.target.y(),\n    })\n  }\n\n  // Handle canvas click\n  const handleStageClick = (e: KonvaEventObject<MouseEvent>) => {\n    // If clicking on empty area\n    if (e.target === e.target.getStage()) {\n      setSelectedItemId(null)\n\n      if (onCanvasClick) {\n        const stage = e.target.getStage()\n        const pointer = stage?.getPointerPosition()\n        if (pointer) {\n          // Convert screen coordinates to canvas coordinates\n          const canvasX = (pointer.x - viewport.x) / viewport.scale\n          const canvasY = (pointer.y - viewport.y) / viewport.scale\n          onCanvasClick(canvasX, canvasY)\n        }\n      }\n    }\n  }\n\n  // Render timeline item\n  const renderTimelineItem = (item: TimelineItem) => {\n    const isSelected = selectedItemId === item.id\n    const itemDate = new Date(item.date)\n\n    return (\n      <Group\n        key={item.id}\n        x={item.x_position}\n        y={item.y_position}\n        draggable\n        onClick={() => {\n          setSelectedItemId(item.id)\n          onItemClick?.(item)\n        }}\n        onDragStart={() => setIsDragging(true)}\n        onDragEnd={(e) => {\n          setIsDragging(false)\n          onItemMove?.(item.id, e.target.x(), e.target.y())\n        }}\n      >\n        {/* Item circle */}\n        <Circle\n          radius={isSelected ? 12 : 8}\n          fill={isSelected ? '#3b82f6' : '#6b7280'}\n          stroke={isSelected ? '#1d4ed8' : '#374151'}\n          strokeWidth={2}\n        />\n\n        {/* Item title */}\n        <Text\n          text={item.title}\n          x={-50}\n          y={-35}\n          width={100}\n          align=\"center\"\n          fontSize={12}\n          fontFamily=\"Arial\"\n          fill=\"#1f2937\"\n        />\n\n        {/* Item date */}\n        <Text\n          text={itemDate.toLocaleDateString()}\n          x={-50}\n          y={20}\n          width={100}\n          align=\"center\"\n          fontSize={10}\n          fontFamily=\"Arial\"\n          fill=\"#6b7280\"\n        />\n      </Group>\n    )\n  }\n\n  // Draw timeline line connecting items\n  const renderTimelineLine = () => {\n    if (timelineItems.length < 2) return null\n\n    const sortedItems = [...timelineItems].sort((a, b) =>\n      new Date(a.date).getTime() - new Date(b.date).getTime()\n    )\n\n    const points: number[] = []\n    sortedItems.forEach(item => {\n      points.push(item.x_position, item.y_position)\n    })\n\n    return (\n      <Line\n        points={points}\n        stroke=\"#d1d5db\"\n        strokeWidth={2}\n        lineCap=\"round\"\n        lineJoin=\"round\"\n      />\n    )\n  }\n\n  return (\n    <div className=\"w-full h-full bg-gray-50\">\n      <Stage\n        ref={stageRef}\n        width={stageSize.width}\n        height={stageSize.height}\n        x={viewport.x}\n        y={viewport.y}\n        scaleX={viewport.scale}\n        scaleY={viewport.scale}\n        draggable={!isDragging}\n        onWheel={handleWheel}\n        onDragEnd={handleStageDragEnd}\n        onClick={handleStageClick}\n      >\n        <Layer>\n          {/* Timeline line */}\n          {renderTimelineLine()}\n\n          {/* Timeline items */}\n          {timelineItems.map(renderTimelineItem)}\n        </Layer>\n      </Stage>\n    </div>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;AAJA;;;;;AAOA,4CAA4C;AAC5C,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;sBAA4D;;;;;;;AAUrF,MAAM,iBAAgD,CAAC,EAC5D,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACd;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAK,QAAQ;IAAI;IAErE,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,aAAa,EACd,GAAG,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAEnB,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,YAAY,SAAS,OAAO,CAAC,SAAS,GAAG,aAAa;gBAC5D,aAAa;oBACX,OAAO,UAAU,WAAW;oBAC5B,QAAQ,UAAU,YAAY;gBAChC;YACF;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,cAAc,CAAC;QACnB,EAAE,GAAG,CAAC,cAAc;QAEpB,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ;QAC/B,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,SAAS,KAAK;QAC/B,MAAM,UAAU,MAAM,kBAAkB;QACxC,IAAI,CAAC,SAAS;QAEd,MAAM,eAAe;YACnB,GAAG,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,IAAI;YAC9B,GAAG,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,IAAI;QAChC;QAEA,MAAM,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI;QAC1C,MAAM,UAAU;QAChB,MAAM,WAAW,YAAY,IAAI,WAAW,UAAU,WAAW;QAEjE,oBAAoB;QACpB,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;QAE/C,MAAM,SAAS;YACb,GAAG,QAAQ,CAAC,GAAG,aAAa,CAAC,GAAG;YAChC,GAAG,QAAQ,CAAC,GAAG,aAAa,CAAC,GAAG;QAClC;QAEA,YAAY;YACV,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,qBAAqB,CAAC;QAC1B,YAAY;YACV,GAAG,QAAQ;YACX,GAAG,EAAE,MAAM,CAAC,CAAC;YACb,GAAG,EAAE,MAAM,CAAC,CAAC;QACf;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,4BAA4B;QAC5B,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,CAAC,QAAQ,IAAI;YACpC,kBAAkB;YAElB,IAAI,eAAe;gBACjB,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBAC/B,MAAM,UAAU,OAAO;gBACvB,IAAI,SAAS;oBACX,mDAAmD;oBACnD,MAAM,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,IAAI,SAAS,KAAK;oBACzD,MAAM,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,IAAI,SAAS,KAAK;oBACzD,cAAc,SAAS;gBACzB;YACF;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,mBAAmB,KAAK,EAAE;QAC7C,MAAM,WAAW,IAAI,KAAK,KAAK,IAAI;QAEnC,qBACE,8OAAC;YAEC,GAAG,KAAK,UAAU;YAClB,GAAG,KAAK,UAAU;YAClB,SAAS;YACT,SAAS;gBACP,kBAAkB,KAAK,EAAE;gBACzB,cAAc;YAChB;YACA,aAAa,IAAM,cAAc;YACjC,WAAW,CAAC;gBACV,cAAc;gBACd,aAAa,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAChD;;8BAGA,8OAAC;oBACC,QAAQ,aAAa,KAAK;oBAC1B,MAAM,aAAa,YAAY;oBAC/B,QAAQ,aAAa,YAAY;oBACjC,aAAa;;;;;;8BAIf,8OAAC;oBACC,MAAM,KAAK,KAAK;oBAChB,GAAG,CAAC;oBACJ,GAAG,CAAC;oBACJ,OAAO;oBACP,OAAM;oBACN,UAAU;oBACV,YAAW;oBACX,MAAK;;;;;;8BAIP,8OAAC;oBACC,MAAM,SAAS,kBAAkB;oBACjC,GAAG,CAAC;oBACJ,GAAG;oBACH,OAAO;oBACP,OAAM;oBACN,UAAU;oBACV,YAAW;oBACX,MAAK;;;;;;;WA3CF,KAAK,EAAE;;;;;IA+ClB;IAEA,sCAAsC;IACtC,MAAM,qBAAqB;QACzB,IAAI,cAAc,MAAM,GAAG,GAAG,OAAO;QAErC,MAAM,cAAc;eAAI;SAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAC9C,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;QAGvD,MAAM,SAAmB,EAAE;QAC3B,YAAY,OAAO,CAAC,CAAA;YAClB,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE,KAAK,UAAU;QAC9C;QAEA,qBACE,8OAAC;YACC,QAAQ;YACR,QAAO;YACP,aAAa;YACb,SAAQ;YACR,UAAS;;;;;;IAGf;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,OAAO,UAAU,KAAK;YACtB,QAAQ,UAAU,MAAM;YACxB,GAAG,SAAS,CAAC;YACb,GAAG,SAAS,CAAC;YACb,QAAQ,SAAS,KAAK;YACtB,QAAQ,SAAS,KAAK;YACtB,WAAW,CAAC;YACZ,SAAS;YACT,WAAW;YACX,SAAS;sBAET,cAAA,8OAAC;;oBAEE;oBAGA,cAAc,GAAG,CAAC;;;;;;;;;;;;;;;;;AAK7B", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-timeline-items.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabase } from '@/lib/supabase'\nimport { TimelineItem, CreateTimelineItemData, UpdateTimelineItemData } from '@/types/timeline'\n\n// Fetch all items for a specific timeline\nexport const useTimelineItems = (timelineId: string) => {\n  return useQuery({\n    queryKey: ['timeline-items', timelineId],\n    queryFn: async (): Promise<TimelineItem[]> => {\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .select('*')\n        .eq('timeline_id', timelineId)\n        .order('date', { ascending: true })\n\n      if (error) throw error\n      return data || []\n    },\n    enabled: !!timelineId,\n  })\n}\n\n// Create a new timeline item\nexport const useCreateTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (itemData: CreateTimelineItemData): Promise<TimelineItem> => {\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .insert(itemData)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n\n// Update a timeline item\nexport const useUpdateTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineItemData }): Promise<TimelineItem> => {\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .update(updates)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n\n// Delete a timeline item\nexport const useDeleteTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (id: string): Promise<{ timeline_id: string }> => {\n      // First get the timeline_id before deleting\n      const { data: item, error: fetchError } = await supabase\n        .from('timeline_items')\n        .select('timeline_id')\n        .eq('id', id)\n        .single()\n\n      if (fetchError) throw fetchError\n\n      const { error } = await supabase\n        .from('timeline_items')\n        .delete()\n        .eq('id', id)\n\n      if (error) throw error\n      return { timeline_id: item.timeline_id }\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;;;AAIO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAW;QACxC,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAK;YAEnC,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,OAAO,EAAmD;YACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,4CAA4C;YAC5C,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrD,IAAI,CAAC,kBACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,YAAY,MAAM;YAEtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,aAAa,KAAK,WAAW;YAAC;QACzC;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { AuthForm } from '@/components/auth/auth-form'\nimport { TimelineList } from '@/components/timeline/timeline-list'\nimport { TimelineCanvas } from '@/components/timeline/timeline-canvas'\nimport { useAuth } from '@/hooks/use-auth'\nimport { useTimelineItems, useCreateTimelineItem, useUpdateTimelineItem } from '@/hooks/use-timeline-items'\nimport { Timeline, TimelineItem } from '@/types/timeline'\nimport { Button } from '@/components/ui/button'\nimport { LogOut, ArrowLeft } from 'lucide-react'\n\nexport default function Home() {\n  const { user, loading, signOut } = useAuth()\n  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')\n  const [selectedTimeline, setSelectedTimeline] = useState<Timeline | null>(null)\n  const [selectedItem, setSelectedItem] = useState<TimelineItem | null>(null)\n\n  const { data: timelineItems = [], refetch } = useTimelineItems(selectedTimeline?.id || '')\n  const createItemMutation = useCreateTimelineItem()\n  const updateItemMutation = useUpdateTimelineItem()\n\n  const handleSignOut = async () => {\n    await signOut()\n    setSelectedTimeline(null)\n    setSelectedItem(null)\n  }\n\n  const handleTimelineSelect = (timeline: Timeline) => {\n    setSelectedTimeline(timeline)\n  }\n\n  const handleBackToTimelines = () => {\n    setSelectedTimeline(null)\n    setSelectedItem(null)\n  }\n\n  const handleCanvasClick = async (x: number, y: number) => {\n    if (!selectedTimeline) return\n\n    const newItem = {\n      timeline_id: selectedTimeline.id,\n      title: 'New Event',\n      description: 'Click to edit',\n      date: new Date().toISOString(),\n      x_position: x,\n      y_position: y,\n    }\n\n    try {\n      await createItemMutation.mutateAsync(newItem)\n      refetch()\n    } catch (error) {\n      console.error('Failed to create timeline item:', error)\n    }\n  }\n\n  const handleItemMove = async (itemId: string, x: number, y: number) => {\n    try {\n      await updateItemMutation.mutateAsync({\n        id: itemId,\n        updates: { x_position: x, y_position: y }\n      })\n      refetch()\n    } catch (error) {\n      console.error('Failed to update timeline item:', error)\n    }\n  }\n\n  const handleItemClick = (item: TimelineItem) => {\n    setSelectedItem(item)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg text-gray-600\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <AuthForm mode={authMode} onModeChange={setAuthMode} />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-4\">\n              {selectedTimeline && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleBackToTimelines}\n                >\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back to Timelines\n                </Button>\n              )}\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                {selectedTimeline ? selectedTimeline.title : 'Timeline App'}\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">{user.email}</span>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleSignOut}>\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                Sign Out\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!selectedTimeline ? (\n          <TimelineList onTimelineSelect={handleTimelineSelect} />\n        ) : (\n          <div className=\"h-[calc(100vh-200px)] bg-white rounded-lg shadow-sm border\">\n            <TimelineCanvas\n              timelineItems={timelineItems}\n              onItemClick={handleItemClick}\n              onItemMove={handleItemMove}\n              onCanvasClick={handleCanvasClick}\n            />\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEtE,MAAM,EAAE,MAAM,gBAAgB,EAAE,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,MAAM;IACvF,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAC/C,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAE/C,MAAM,gBAAgB;QACpB,MAAM;QACN,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,OAAO,GAAW;QAC1C,IAAI,CAAC,kBAAkB;QAEvB,MAAM,UAAU;YACd,aAAa,iBAAiB,EAAE;YAChC,OAAO;YACP,aAAa;YACb,MAAM,IAAI,OAAO,WAAW;YAC5B,YAAY;YACZ,YAAY;QACd;QAEA,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB,OAAO,QAAgB,GAAW;QACvD,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBACnC,IAAI;gBACJ,SAAS;oBAAE,YAAY;oBAAG,YAAY;gBAAE;YAC1C;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAU,cAAc;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,kCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI1C,8OAAC;wCAAG,WAAU;kDACX,mBAAmB,iBAAiB,KAAK,GAAG;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAyB,KAAK,KAAK;;;;;;kDACnD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAK,WAAU;0BACb,CAAC,iCACA,8OAAC,kJAAA,CAAA,eAAY;oBAAC,kBAAkB;;;;;yCAEhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oJAAA,CAAA,iBAAc;wBACb,eAAe;wBACf,aAAa;wBACb,YAAY;wBACZ,eAAe;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}