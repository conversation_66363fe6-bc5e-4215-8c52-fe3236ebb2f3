'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

export default function AuthCallback() {
  const router = useRouter()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')

  useEffect(() => {
    const handleAuthCallback = async () => {
      if (!supabase) {
        setStatus('error')
        setMessage('Authentication service not available')
        return
      }

      try {
        // Get the URL hash parameters
        const hashParams = new URLSearchParams(window.location.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')
        const type = hashParams.get('type')

        if (type === 'signup' && accessToken && refreshToken) {
          // Set the session with the tokens
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          })

          if (error) {
            console.error('Error setting session:', error)
            setStatus('error')
            setMessage('Failed to confirm email. Please try again.')
          } else if (data.user) {
            setStatus('success')
            setMessage('Email confirmed successfully! You are now signed in.')
            
            // Redirect to home page after a short delay
            setTimeout(() => {
              router.push('/')
            }, 2000)
          }
        } else {
          // Try to get session normally (for other auth flows)
          const { data, error } = await supabase.auth.getSession()
          
          if (error) {
            console.error('Error getting session:', error)
            setStatus('error')
            setMessage('Authentication failed. Please try signing in again.')
          } else if (data.session) {
            setStatus('success')
            setMessage('Successfully authenticated!')
            router.push('/')
          } else {
            setStatus('error')
            setMessage('No valid authentication found. Please try signing in again.')
          }
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        setStatus('error')
        setMessage('An unexpected error occurred. Please try again.')
      }
    }

    handleAuthCallback()
  }, [router])

  const handleReturnHome = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {status === 'loading' && (
              <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />
            )}
            {status === 'success' && (
              <CheckCircle className="w-12 h-12 text-green-500" />
            )}
            {status === 'error' && (
              <XCircle className="w-12 h-12 text-red-500" />
            )}
          </div>
          
          <CardTitle>
            {status === 'loading' && 'Confirming Email...'}
            {status === 'success' && 'Email Confirmed!'}
            {status === 'error' && 'Confirmation Failed'}
          </CardTitle>
          
          <CardDescription>
            {message}
          </CardDescription>
        </CardHeader>
        
        {status !== 'loading' && (
          <CardContent>
            <Button 
              onClick={handleReturnHome} 
              className="w-full"
              variant={status === 'success' ? 'default' : 'outline'}
            >
              {status === 'success' ? 'Continue to App' : 'Return to Sign In'}
            </Button>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
