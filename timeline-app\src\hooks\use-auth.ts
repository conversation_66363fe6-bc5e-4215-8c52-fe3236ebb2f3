import { useEffect, useState, useCallback } from 'react'
import { User, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

// Mock user for development
const createMockUser = (email: string): User => ({
  id: `mock-${Date.now()}`,
  email,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: new Date().toISOString(),
  last_sign_in_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: {},
  identities: [],
  factors: [],
})

export interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
  isEmailConfirmationPending: boolean
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEmailConfirmationPending, setIsEmailConfirmationPending] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [lastErrorTime, setLastErrorTime] = useState<number | null>(null)

  // Helper function to handle errors with retry logic
  const handleError = useCallback((errorMessage: string) => {
    const now = Date.now()
    const timeSinceLastError = lastErrorTime ? now - lastErrorTime : Infinity

    // Reset retry count if enough time has passed (5 minutes)
    if (timeSinceLastError > 5 * 60 * 1000) {
      setRetryCount(1)
    } else {
      setRetryCount(prev => prev + 1)
    }

    setLastErrorTime(now)

    // Only show error after 3 attempts to protect server
    if (retryCount >= 2) {
      setError(errorMessage)
    }
  }, [lastErrorTime, retryCount])

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') {
      setLoading(false)
      return
    }

    if (!supabase) {
      // Mock authentication for development
      const mockUser = localStorage.getItem('mock-user')
      if (mockUser) {
        setUser(JSON.parse(mockUser))
      }
      setLoading(false)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        if (!supabase) {
          setLoading(false)
          return
        }

        const { data: { session }, error } = await supabase.auth.getSession()
        if (error) {
          console.error('Error getting session:', error)
          handleError(error.message)
        } else {
          setUser(session?.user ?? null)
        }
      } catch (error) {
        console.error('Unexpected error getting session:', error)
        handleError('Failed to get session')
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes only if supabase is available
    if (supabase) {
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          console.log('Auth state changed:', event, session?.user?.email)

          setUser(session?.user ?? null)
          setLoading(false)
          setError(null)

          // Handle email confirmation
          if (event === 'SIGNED_IN' && session?.user) {
            setIsEmailConfirmationPending(false)
          }

          if (event === 'TOKEN_REFRESHED') {
            console.log('Token refreshed')
          }
        }
      )

      return () => subscription.unsubscribe()
    }
  }, [handleError])

  const signIn = async (email: string, password: string) => {
    setError(null)
    setLoading(true)

    try {
      if (!supabase) {
        // Mock authentication for development
        if (password.length >= 6) {
          const mockUser = createMockUser(email)
          localStorage.setItem('mock-user', JSON.stringify(mockUser))
          setUser(mockUser)
          return { data: { user: mockUser, session: null }, error: null }
        } else {
          const error = { message: 'Password must be at least 6 characters' }
          setError(error.message)
          return { data: null, error }
        }
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password,
      })

      if (error) {
        // Provide user-friendly error messages
        let errorMessage = error.message
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.'
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Please check your email and click the confirmation link before signing in.'
          setIsEmailConfirmationPending(true)
        }
        handleError(errorMessage)
      }

      return { data, error }
    } catch {
      const errorMessage = 'An unexpected error occurred. Please try again.'
      handleError(errorMessage)
      return { data: null, error: { message: errorMessage } }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    setError(null)
    setLoading(true)

    try {
      if (!supabase) {
        // Mock authentication for development
        if (password.length >= 6) {
          const mockUser = createMockUser(email)
          localStorage.setItem('mock-user', JSON.stringify(mockUser))
          setUser(mockUser)
          return { data: { user: mockUser, session: null }, error: null }
        } else {
          const error = { message: 'Password must be at least 6 characters' }
          setError(error.message)
          return { data: null, error }
        }
      }

      const { data, error } = await supabase.auth.signUp({
        email: email.trim().toLowerCase(),
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) {
        // Provide user-friendly error messages
        let errorMessage = error.message
        if (error.message.includes('User already registered')) {
          errorMessage = 'An account with this email already exists. Please sign in instead.'
        } else if (error.message.includes('Password should be at least')) {
          errorMessage = 'Password must be at least 6 characters long.'
        }
        handleError(errorMessage)
      } else if (data.user && !data.session) {
        // Email confirmation required
        setIsEmailConfirmationPending(true)
        setError(null)
      }

      return { data, error }
    } catch {
      const errorMessage = 'An unexpected error occurred. Please try again.'
      handleError(errorMessage)
      return { data: null, error: { message: errorMessage } }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setError(null)
    setLoading(true)

    try {
      if (!supabase) {
        // Mock sign out
        localStorage.removeItem('mock-user')
        setUser(null)
        return { error: null }
      }

      const { error } = await supabase.auth.signOut()
      if (error) {
        setError(error.message)
      } else {
        setUser(null)
        setIsEmailConfirmationPending(false)
      }
      return { error }
    } catch {
      const errorMessage = 'Failed to sign out. Please try again.'
      setError(errorMessage)
      return { error: { message: errorMessage } }
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    setError(null)

    try {
      if (!supabase) {
        // Mock password reset
        return { data: null, error: null }
      }

      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })

      if (error) {
        setError(error.message)
      }

      return { data, error }
    } catch {
      const errorMessage = 'Failed to send reset email. Please try again.'
      setError(errorMessage)
      return { data: null, error: { message: errorMessage } }
    }
  }

  const resendConfirmation = async (email: string) => {
    setError(null)

    try {
      if (!supabase) {
        return { data: null, error: null }
      }

      const { data, error } = await supabase.auth.resend({
        type: 'signup',
        email: email.trim().toLowerCase(),
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) {
        setError(error.message)
      }

      return { data, error }
    } catch {
      const errorMessage = 'Failed to resend confirmation email. Please try again.'
      setError(errorMessage)
      return { data: null, error: { message: errorMessage } }
    }
  }

  return {
    user,
    loading,
    error,
    isEmailConfirmationPending,
    signIn,
    signUp,
    signOut,
    resetPassword,
    resendConfirmation,
    clearError: () => setError(null),
  }
}
