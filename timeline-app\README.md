# Timeline App

A modern 2D timeline application built with Next.js 14, TypeScript, and Supabase. Create and manage interactive timelines with drag-and-drop functionality, inspired by <PERSON><PERSON>'s design approach.

## ✨ Features

- **🔐 Authentication**: Secure user registration and login with <PERSON>pa<PERSON> Auth
- **📊 Timeline Management**: Create, edit, and delete timelines
- **🎨 Interactive Canvas**: 2D canvas with zoom, pan, and drag-and-drop functionality
- **⚡ Real-time Updates**: Live synchronization with Supabase database
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices
- **🎯 Modern UI**: Built with shadcn/ui components and Tailwind CSS

## 🚀 Technology Stack

### Frontend
- **Next.js 14** with App Router
- **TypeScript** with strict mode
- **React 19** with modern hooks
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **Konva.js** for 2D canvas rendering
- **Framer Motion** for animations
- **Lucide React** for icons

### Backend & Database
- **Supabase** for authentication and database
- **PostgreSQL** with Row Level Security
- **Real-time subscriptions** for live updates

### State Management
- **Zustand** for client-side state
- **TanStack Query** for server state management

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account (free tier available)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd timeline-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new Supabase project
   - Follow the [Supabase Setup Guide](./docs/backend/supabase-setup.md)
   - Run the database schema from the documentation

4. **Configure environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Documentation

- [📚 Complete Documentation](./docs/README.md)
- [🎨 Frontend Components](./docs/frontend/components.md)
- [🗄️ Database Schema](./docs/backend/database-schema.md)
- [⚙️ Supabase Setup](./docs/backend/supabase-setup.md)

## 🏗️ Project Structure

```
timeline-app/
├── docs/                          # Documentation
├── src/
│   ├── app/                       # Next.js App Router
│   ├── components/                # React components
│   │   ├── ui/                    # shadcn/ui components
│   │   ├── timeline/              # Timeline-specific components
│   │   ├── auth/                  # Authentication components
│   │   └── providers/             # Context providers
│   ├── hooks/                     # Custom React hooks
│   ├── lib/                       # Utilities and configurations
│   ├── stores/                    # Zustand stores
│   ├── types/                     # TypeScript type definitions
│   └── styles/                    # Global styles
├── public/                        # Static assets
└── supabase/                      # Database migrations & types
```

## 🎯 Usage

1. **Sign up** for a new account or **sign in** with existing credentials
2. **Create a timeline** by clicking the "New Timeline" button
3. **Add events** by clicking anywhere on the canvas
4. **Drag items** to reposition them on the timeline
5. **Zoom and pan** to navigate large timelines
6. **Edit items** by clicking on them (feature in development)

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms

The app can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Development Guidelines

- Use TypeScript strict mode
- Follow React best practices with functional components and hooks
- Use shadcn/ui components when possible
- Implement proper error handling and loading states
- Write modular, reusable code
- Document complex functionality

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Supabase](https://supabase.com/) for the backend infrastructure
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Konva.js](https://konvajs.org/) for 2D canvas functionality
- [Tailwind CSS](https://tailwindcss.com/) for utility-first styling
