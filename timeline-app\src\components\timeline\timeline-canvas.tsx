'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Stage, Layer, Line, Circle, Text, Group } from 'react-konva'
import { KonvaEventObject } from 'konva/lib/Node'
import { useTimelineStore } from '@/stores/timeline-store'
import { TimelineItem } from '@/types/timeline'

interface TimelineCanvasProps {
  timelineItems: TimelineItem[]
  onItemClick?: (item: TimelineItem) => void
  onItemMove?: (itemId: string, x: number, y: number) => void
  onCanvasClick?: (x: number, y: number) => void
}

export const TimelineCanvas = ({
  timelineItems,
  onItemClick,
  onItemMove,
  onCanvasClick,
}) => {
  const stageRef = useRef<any>(null)
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 })
  
  const {
    viewport,
    selectedItemId,
    isDragging,
    setViewport,
    setSelectedItemId,
    setIsDragging,
  } = useTimelineStore()

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (stageRef.current) {
        const container = stageRef.current.container().parentElement
        setStageSize({
          width: container.offsetWidth,
          height: container.offsetHeight,
        })
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle wheel zoom
  const handleWheel = (e: KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault()
    
    const stage = e.target.getStage()
    if (!stage) return

    const oldScale = viewport.scale
    const pointer = stage.getPointerPosition()
    if (!pointer) return

    const mousePointTo = {
      x: (pointer.x - viewport.x) / oldScale,
      y: (pointer.y - viewport.y) / oldScale,
    }

    const direction = e.evt.deltaY > 0 ? -1 : 1
    const scaleBy = 1.1
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy

    // Limit zoom levels
    const clampedScale = Math.max(0.1, Math.min(5, newScale))

    const newPos = {
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    }

    setViewport({
      x: newPos.x,
      y: newPos.y,
      scale: clampedScale,
    })
  }

  // Handle stage drag
  const handleStageDragEnd = (e: KonvaEventObject<DragEvent>) => {
    setViewport({
      ...viewport,
      x: e.target.x(),
      y: e.target.y(),
    })
  }

  // Handle canvas click
  const handleStageClick = (e: KonvaEventObject<MouseEvent>) => {
    // If clicking on empty area
    if (e.target === e.target.getStage()) {
      setSelectedItemId(null)
      
      if (onCanvasClick) {
        const stage = e.target.getStage()
        const pointer = stage?.getPointerPosition()
        if (pointer) {
          // Convert screen coordinates to canvas coordinates
          const canvasX = (pointer.x - viewport.x) / viewport.scale
          const canvasY = (pointer.y - viewport.y) / viewport.scale
          onCanvasClick(canvasX, canvasY)
        }
      }
    }
  }

  // Render timeline item
  const renderTimelineItem = (item: TimelineItem) => {
    const isSelected = selectedItemId === item.id
    const itemDate = new Date(item.date)
    
    return (
      <Group
        key={item.id}
        x={item.x_position}
        y={item.y_position}
        draggable
        onClick={() => {
          setSelectedItemId(item.id)
          onItemClick?.(item)
        }}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={(e) => {
          setIsDragging(false)
          onItemMove?.(item.id, e.target.x(), e.target.y())
        }}
      >
        {/* Item circle */}
        <Circle
          radius={isSelected ? 12 : 8}
          fill={isSelected ? '#3b82f6' : '#6b7280'}
          stroke={isSelected ? '#1d4ed8' : '#374151'}
          strokeWidth={2}
        />
        
        {/* Item title */}
        <Text
          text={item.title}
          x={-50}
          y={-35}
          width={100}
          align="center"
          fontSize={12}
          fontFamily="Arial"
          fill="#1f2937"
        />
        
        {/* Item date */}
        <Text
          text={itemDate.toLocaleDateString()}
          x={-50}
          y={20}
          width={100}
          align="center"
          fontSize={10}
          fontFamily="Arial"
          fill="#6b7280"
        />
      </Group>
    )
  }

  // Draw timeline line connecting items
  const renderTimelineLine = () => {
    if (timelineItems.length < 2) return null

    const sortedItems = [...timelineItems].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    )

    const points: number[] = []
    sortedItems.forEach(item => {
      points.push(item.x_position, item.y_position)
    })

    return (
      <Line
        points={points}
        stroke="#d1d5db"
        strokeWidth={2}
        lineCap="round"
        lineJoin="round"
      />
    )
  }

  return (
    <div className="w-full h-full bg-gray-50">
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        x={viewport.x}
        y={viewport.y}
        scaleX={viewport.scale}
        scaleY={viewport.scale}
        draggable={!isDragging}
        onWheel={handleWheel}
        onDragEnd={handleStageDragEnd}
        onClick={handleStageClick}
      >
        <Layer>
          {/* Timeline line */}
          {renderTimelineLine()}
          
          {/* Timeline items */}
          {timelineItems.map(renderTimelineItem)}
        </Layer>
      </Stage>
    </div>
  )
}

