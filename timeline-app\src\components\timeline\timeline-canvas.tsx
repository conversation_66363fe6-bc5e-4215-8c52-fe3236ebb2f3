'use client'

import React from 'react'
import { TimelineItem } from '@/types/timeline'

interface TimelineCanvasProps {
  timelineItems: TimelineItem[]
  onItemClick?: (item: TimelineItem) => void
  onItemMove?: (itemId: string, x: number, y: number) => void
  onCanvasClick?: (x: number, y: number) => void
}

export const TimelineCanvas: React.FC<TimelineCanvasProps> = ({
  timelineItems,
  onItemClick,
  onItemMove,
  onCanvasClick,
}) => {
  const handleCanvasClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    onCanvasClick?.(x, y)
  }

  const handleItemClick = (item: TimelineItem, e: React.MouseEvent) => {
    e.stopPropagation()
    onItemClick?.(item)
  }

  return (
    <div
      className="w-full h-full bg-gray-50 relative overflow-hidden cursor-crosshair"
      onClick={handleCanvasClick}
    >
      {/* Grid background */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(to right, #e5e7eb 1px, transparent 1px),
            linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />

      {/* Timeline items */}
      {timelineItems.map((item) => (
        <div
          key={item.id}
          className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
          style={{
            left: item.x_position,
            top: item.y_position,
          }}
          onClick={(e) => handleItemClick(item, e)}
        >
          {/* Item circle */}
          <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-blue-700 group-hover:w-5 group-hover:h-5 transition-all duration-200" />

          {/* Item title */}
          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 whitespace-nowrap bg-white px-2 py-1 rounded shadow-sm">
            {item.title}
          </div>

          {/* Item date */}
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
            {new Date(item.date).toLocaleDateString()}
          </div>
        </div>
      ))}

      {/* Timeline line connecting items */}
      {timelineItems.length > 1 && (
        <svg className="absolute inset-0 pointer-events-none">
          <path
            d={timelineItems
              .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
              .map((item, index) =>
                `${index === 0 ? 'M' : 'L'} ${item.x_position} ${item.y_position}`
              )
              .join(' ')
            }
            stroke="#d1d5db"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}

      {/* Instructions */}
      {timelineItems.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="text-lg font-medium mb-2">Click anywhere to add your first timeline event</div>
            <div className="text-sm">Events will be connected chronologically</div>
          </div>
        </div>
      )}
    </div>
  )
}


