import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { Timeline, CreateTimelineData, UpdateTimelineData } from '@/types/timeline'

// Mock data storage
const getMockTimelines = (): Timeline[] => {
  const stored = localStorage.getItem('mock-timelines')
  return stored ? JSON.parse(stored) : []
}

const setMockTimelines = (timelines: Timeline[]) => {
  localStorage.setItem('mock-timelines', JSON.stringify(timelines))
}

// Fetch all timelines for the current user
export const useTimelines = () => {
  return useQuery({
    queryKey: ['timelines'],
    queryFn: async (): Promise<Timeline[]> => {
      if (!supabase) {
        // Mock data for development
        return getMockTimelines()
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('timelines')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })

      if (error) throw error
      return data || []
    },
  })
}

// Fetch a single timeline by ID
export const useTimeline = (id: string) => {
  return useQuery({
    queryKey: ['timeline', id],
    queryFn: async (): Promise<Timeline> => {
      if (!supabase) {
        // Mock data for development
        const timelines = getMockTimelines()
        const timeline = timelines.find(t => t.id === id)
        if (!timeline) throw new Error('Timeline not found')
        return timeline
      }

      const { data, error } = await supabase
        .from('timelines')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!id,
  })
}

// Create a new timeline
export const useCreateTimeline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (timelineData: CreateTimelineData): Promise<Timeline> => {
      if (!supabase) {
        // Mock data for development
        const mockUser = localStorage.getItem('mock-user')
        if (!mockUser) throw new Error('User not authenticated')

        const user = JSON.parse(mockUser)
        const newTimeline: Timeline = {
          id: `timeline-${Date.now()}`,
          title: timelineData.title,
          description: timelineData.description || null,
          user_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }

        const timelines = getMockTimelines()
        timelines.push(newTimeline)
        setMockTimelines(timelines)
        return newTimeline
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('timelines')
        .insert({
          ...timelineData,
          user_id: user.id,
        })
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timelines'] })
    },
  })
}

// Update a timeline
export const useUpdateTimeline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineData }): Promise<Timeline> => {
      if (!supabase) {
        // Mock data for development
        const timelines = getMockTimelines()
        const timelineIndex = timelines.findIndex(t => t.id === id)
        if (timelineIndex === -1) throw new Error('Timeline not found')

        const updatedTimeline = {
          ...timelines[timelineIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        }

        timelines[timelineIndex] = updatedTimeline
        setMockTimelines(timelines)
        return updatedTimeline
      }

      const { data, error } = await supabase
        .from('timelines')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['timelines'] })
      queryClient.invalidateQueries({ queryKey: ['timeline', data.id] })
    },
  })
}

// Delete a timeline
export const useDeleteTimeline = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      if (!supabase) {
        // Mock data for development
        const timelines = getMockTimelines()
        const filteredTimelines = timelines.filter(t => t.id !== id)
        setMockTimelines(filteredTimelines)

        // Also remove associated timeline items
        const timelineItems = localStorage.getItem('mock-timeline-items')
        if (timelineItems) {
          const items = JSON.parse(timelineItems)
          const filteredItems = items.filter((item: any) => item.timeline_id !== id)
          localStorage.setItem('mock-timeline-items', JSON.stringify(filteredItems))
        }
        return
      }

      const { error } = await supabase
        .from('timelines')
        .delete()
        .eq('id', id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timelines'] })
    },
  })
}
