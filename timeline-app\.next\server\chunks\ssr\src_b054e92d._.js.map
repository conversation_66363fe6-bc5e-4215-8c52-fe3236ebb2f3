{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if we have valid credentials and we're on the client side\nconst hasValidCredentials = supabaseUrl && supabaseAnonKey &&\n  !supabaseUrl.includes('placeholder') &&\n  !supabaseAnonKey.includes('placeholder')\n\nconst isClientSide = typeof window !== 'undefined'\n\nexport const supabase = hasValidCredentials && isClientSide\n  ? createClient(supabaseUrl, supabaseAnonKey, {\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n      }\n    })\n  : null\n\nexport type Database = {\n  public: {\n    Tables: {\n      timelines: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          user_id: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          user_id: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          user_id?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      timeline_items: {\n        Row: {\n          id: string\n          timeline_id: string\n          title: string\n          description: string | null\n          date: string\n          x_position: number\n          y_position: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          timeline_id: string\n          title: string\n          description?: string | null\n          date: string\n          x_position: number\n          y_position: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          timeline_id?: string\n          title?: string\n          description?: string | null\n          date?: string\n          x_position?: number\n          y_position?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA,MAAM;AACN,MAAM;AAEN,kEAAkE;AAClE,MAAM,sBAAsB,eAAe,mBACzC,CAAC,YAAY,QAAQ,CAAC,kBACtB,CAAC,gBAAgB,QAAQ,CAAC;AAE5B,MAAM,eAAe,gBAAkB;AAEhC,MAAM,WAAW,6EASpB", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-auth.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\nimport { User, AuthError } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\n// Mock user for development\nconst createMockUser = (email: string): User => ({\n  id: `mock-${Date.now()}`,\n  email,\n  created_at: new Date().toISOString(),\n  updated_at: new Date().toISOString(),\n  aud: 'authenticated',\n  role: 'authenticated',\n  email_confirmed_at: new Date().toISOString(),\n  last_sign_in_at: new Date().toISOString(),\n  app_metadata: {},\n  user_metadata: {},\n  identities: [],\n  factors: [],\n})\n\nexport interface AuthState {\n  user: User | null\n  loading: boolean\n  error: string | null\n  isEmailConfirmationPending: boolean\n}\n\nexport const useAuth = () => {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [isEmailConfirmationPending, setIsEmailConfirmationPending] = useState(false)\n  const [retryCount, setRetryCount] = useState(0)\n  const [lastErrorTime, setLastErrorTime] = useState<number | null>(null)\n\n  // Helper function to handle errors with retry logic\n  const handleError = (errorMessage: string) => {\n    const now = Date.now()\n    const timeSinceLastError = lastErrorTime ? now - lastErrorTime : Infinity\n\n    // Reset retry count if enough time has passed (5 minutes)\n    if (timeSinceLastError > 5 * 60 * 1000) {\n      setRetryCount(1)\n    } else {\n      setRetryCount(prev => prev + 1)\n    }\n\n    setLastErrorTime(now)\n\n    // Only show error after 3 attempts to protect server\n    if (retryCount >= 2) {\n      setError(errorMessage)\n    }\n  }\n\n  useEffect(() => {\n    if (!supabase) {\n      // Mock authentication for development\n      const mockUser = localStorage.getItem('mock-user')\n      if (mockUser) {\n        setUser(JSON.parse(mockUser))\n      }\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n        if (error) {\n          console.error('Error getting session:', error)\n          setError(error.message)\n        } else {\n          setUser(session?.user ?? null)\n        }\n      } catch (err) {\n        console.error('Unexpected error getting session:', err)\n        setError('Failed to get session')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session?.user?.email)\n\n        setUser(session?.user ?? null)\n        setLoading(false)\n        setError(null)\n\n        // Handle email confirmation\n        if (event === 'SIGNED_IN' && session?.user) {\n          setIsEmailConfirmationPending(false)\n        }\n\n        if (event === 'TOKEN_REFRESHED') {\n          console.log('Token refreshed')\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    setError(null)\n    setLoading(true)\n\n    try {\n      if (!supabase) {\n        // Mock authentication for development\n        if (password.length >= 6) {\n          const mockUser = createMockUser(email)\n          localStorage.setItem('mock-user', JSON.stringify(mockUser))\n          setUser(mockUser)\n          return { data: { user: mockUser, session: null }, error: null }\n        } else {\n          const error = { message: 'Password must be at least 6 characters' }\n          setError(error.message)\n          return { data: null, error }\n        }\n      }\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email: email.trim().toLowerCase(),\n        password,\n      })\n\n      if (error) {\n        // Provide user-friendly error messages\n        let errorMessage = error.message\n        if (error.message.includes('Invalid login credentials')) {\n          errorMessage = 'Invalid email or password. Please check your credentials and try again.'\n        } else if (error.message.includes('Email not confirmed')) {\n          errorMessage = 'Please check your email and click the confirmation link before signing in.'\n          setIsEmailConfirmationPending(true)\n        }\n        handleError(errorMessage)\n      }\n\n      return { data, error }\n    } catch (err) {\n      const errorMessage = 'An unexpected error occurred. Please try again.'\n      handleError(errorMessage)\n      return { data: null, error: { message: errorMessage } }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    setError(null)\n    setLoading(true)\n\n    try {\n      if (!supabase) {\n        // Mock authentication for development\n        if (password.length >= 6) {\n          const mockUser = createMockUser(email)\n          localStorage.setItem('mock-user', JSON.stringify(mockUser))\n          setUser(mockUser)\n          return { data: { user: mockUser, session: null }, error: null }\n        } else {\n          const error = { message: 'Password must be at least 6 characters' }\n          setError(error.message)\n          return { data: null, error }\n        }\n      }\n\n      const { data, error } = await supabase.auth.signUp({\n        email: email.trim().toLowerCase(),\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n\n      if (error) {\n        // Provide user-friendly error messages\n        let errorMessage = error.message\n        if (error.message.includes('User already registered')) {\n          errorMessage = 'An account with this email already exists. Please sign in instead.'\n        } else if (error.message.includes('Password should be at least')) {\n          errorMessage = 'Password must be at least 6 characters long.'\n        }\n        handleError(errorMessage)\n      } else if (data.user && !data.session) {\n        // Email confirmation required\n        setIsEmailConfirmationPending(true)\n        setError(null)\n      }\n\n      return { data, error }\n    } catch (err) {\n      const errorMessage = 'An unexpected error occurred. Please try again.'\n      handleError(errorMessage)\n      return { data: null, error: { message: errorMessage } }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    setError(null)\n    setLoading(true)\n\n    try {\n      if (!supabase) {\n        // Mock sign out\n        localStorage.removeItem('mock-user')\n        setUser(null)\n        return { error: null }\n      }\n\n      const { error } = await supabase.auth.signOut()\n      if (error) {\n        setError(error.message)\n      } else {\n        setUser(null)\n        setIsEmailConfirmationPending(false)\n      }\n      return { error }\n    } catch (err) {\n      const errorMessage = 'Failed to sign out. Please try again.'\n      setError(errorMessage)\n      return { error: { message: errorMessage } }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    setError(null)\n\n    try {\n      if (!supabase) {\n        // Mock password reset\n        return { data: null, error: null }\n      }\n\n      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`\n      })\n\n      if (error) {\n        setError(error.message)\n      }\n\n      return { data, error }\n    } catch (err) {\n      const errorMessage = 'Failed to send reset email. Please try again.'\n      setError(errorMessage)\n      return { data: null, error: { message: errorMessage } }\n    }\n  }\n\n  const resendConfirmation = async (email: string) => {\n    setError(null)\n\n    try {\n      if (!supabase) {\n        return { data: null, error: null }\n      }\n\n      const { data, error } = await supabase.auth.resend({\n        type: 'signup',\n        email: email.trim().toLowerCase(),\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n\n      if (error) {\n        setError(error.message)\n      }\n\n      return { data, error }\n    } catch (err) {\n      const errorMessage = 'Failed to resend confirmation email. Please try again.'\n      setError(errorMessage)\n      return { data: null, error: { message: errorMessage } }\n    }\n  }\n\n  return {\n    user,\n    loading,\n    error,\n    isEmailConfirmationPending,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n    resendConfirmation,\n    clearError: () => setError(null),\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC,QAAwB,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACxB;QACA,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,KAAK;QACL,MAAM;QACN,oBAAoB,IAAI,OAAO,WAAW;QAC1C,iBAAiB,IAAI,OAAO,WAAW;QACvC,cAAc,CAAC;QACf,eAAe,CAAC;QAChB,YAAY,EAAE;QACd,SAAS,EAAE;IACb,CAAC;AASM,MAAM,UAAU;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,oDAAoD;IACpD,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,qBAAqB,gBAAgB,MAAM,gBAAgB;QAEjE,0DAA0D;QAC1D,IAAI,qBAAqB,IAAI,KAAK,MAAM;YACtC,cAAc;QAChB,OAAO;YACL,cAAc,CAAA,OAAQ,OAAO;QAC/B;QAEA,iBAAiB;QAEjB,qDAAqD;QACrD,IAAI,cAAc,GAAG;YACnB,SAAS;QACX;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,sCAAsC;YACtC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,QAAQ,KAAK,KAAK,CAAC;YACrB;YACA,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBACnE,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,SAAS,MAAM,OAAO;gBACxB,OAAO;oBACL,QAAQ,SAAS,QAAQ;gBAC3B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;YAEzD,QAAQ,SAAS,QAAQ;YACzB,WAAW;YACX,SAAS;YAET,4BAA4B;YAC5B,IAAI,UAAU,eAAe,SAAS,MAAM;gBAC1C,8BAA8B;YAChC;YAEA,IAAI,UAAU,mBAAmB;gBAC/B,QAAQ,GAAG,CAAC;YACd;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,sCAAsC;gBACtC,IAAI,SAAS,MAAM,IAAI,GAAG;oBACxB,MAAM,WAAW,eAAe;oBAChC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBACjD,QAAQ;oBACR,OAAO;wBAAE,MAAM;4BAAE,MAAM;4BAAU,SAAS;wBAAK;wBAAG,OAAO;oBAAK;gBAChE,OAAO;oBACL,MAAM,QAAQ;wBAAE,SAAS;oBAAyC;oBAClE,SAAS,MAAM,OAAO;oBACtB,OAAO;wBAAE,MAAM;wBAAM;oBAAM;gBAC7B;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D,OAAO,MAAM,IAAI,GAAG,WAAW;gBAC/B;YACF;YAEA,IAAI,OAAO;gBACT,uCAAuC;gBACvC,IAAI,eAAe,MAAM,OAAO;gBAChC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,8BAA8B;oBACvD,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAwB;oBACxD,eAAe;oBACf,8BAA8B;gBAChC;gBACA,YAAY;YACd;YAEA,OAAO;gBAAE;gBAAM;YAAM;QACvB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe;YACrB,YAAY;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAa;YAAE;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,sCAAsC;gBACtC,IAAI,SAAS,MAAM,IAAI,GAAG;oBACxB,MAAM,WAAW,eAAe;oBAChC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBACjD,QAAQ;oBACR,OAAO;wBAAE,MAAM;4BAAE,MAAM;4BAAU,SAAS;wBAAK;wBAAG,OAAO;oBAAK;gBAChE,OAAO;oBACL,MAAM,QAAQ;wBAAE,SAAS;oBAAyC;oBAClE,SAAS,MAAM,OAAO;oBACtB,OAAO;wBAAE,MAAM;wBAAM;oBAAM;gBAC7B;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD,OAAO,MAAM,IAAI,GAAG,WAAW;gBAC/B;gBACA,SAAS;oBACP,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC5D;YACF;YAEA,IAAI,OAAO;gBACT,uCAAuC;gBACvC,IAAI,eAAe,MAAM,OAAO;gBAChC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,4BAA4B;oBACrD,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,gCAAgC;oBAChE,eAAe;gBACjB;gBACA,YAAY;YACd,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE;gBACrC,8BAA8B;gBAC9B,8BAA8B;gBAC9B,SAAS;YACX;YAEA,OAAO;gBAAE;gBAAM;YAAM;QACvB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe;YACrB,YAAY;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAa;YAAE;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,gBAAgB;gBAChB,aAAa,UAAU,CAAC;gBACxB,QAAQ;gBACR,OAAO;oBAAE,OAAO;gBAAK;YACvB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,QAAQ;gBACR,8BAA8B;YAChC;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe;YACrB,SAAS;YACT,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAa;YAAE;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,SAAS;QAET,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,sBAAsB;gBACtB,OAAO;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACnC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACvE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB;YAEA,OAAO;gBAAE;gBAAM;YAAM;QACvB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe;YACrB,SAAS;YACT,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAa;YAAE;QACxD;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,SAAS;QAET,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,OAAO;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACnC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD,MAAM;gBACN,OAAO,MAAM,IAAI,GAAG,WAAW;gBAC/B,SAAS;oBACP,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC5D;YACF;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB;YAEA,OAAO;gBAAE;gBAAM;YAAM;QACvB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe;YACrB,SAAS;YACT,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAa;YAAE;QACxD;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/auth/auth-form.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuth } from '@/hooks/use-auth'\nimport { AlertCircle, CheckCircle } from 'lucide-react'\n\ninterface AuthFormProps {\n  mode: 'signin' | 'signup'\n  onModeChange: (mode: 'signin' | 'signup') => void\n}\n\nexport const AuthForm: React.FC<AuthFormProps> = ({ mode, onModeChange }) => {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n\n  const [localError, setLocalError] = useState<string | null>(null)\n  const [localMessage, setLocalMessage] = useState<string | null>(null)\n\n  const {\n    signIn,\n    signUp,\n    resetPassword,\n    resendConfirmation,\n    loading,\n    error,\n    isEmailConfirmationPending,\n    clearError\n  } = useAuth()\n\n  // Clear local messages when mode changes\n  useEffect(() => {\n    setLocalError(null)\n    setLocalMessage(null)\n    clearError()\n  }, [mode, clearError])\n\n  const validateForm = () => {\n    if (!email.trim()) {\n      setLocalError('Email is required')\n      return false\n    }\n\n    if (!email.includes('@')) {\n      setLocalError('Please enter a valid email address')\n      return false\n    }\n\n    if (!password) {\n      setLocalError('Password is required')\n      return false\n    }\n\n    if (mode === 'signup' && password.length < 6) {\n      setLocalError('Password must be at least 6 characters long')\n      return false\n    }\n\n    return true\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLocalError(null)\n    setLocalMessage(null)\n    clearError()\n\n    if (!validateForm()) return\n\n    try {\n      if (mode === 'signin') {\n        const { error } = await signIn(email, password)\n        if (!error) {\n          setLocalMessage('Successfully signed in!')\n        }\n      } else {\n        const { data, error } = await signUp(email, password)\n        if (!error && data?.user && !data?.session) {\n          setLocalMessage('Please check your email and click the confirmation link to complete your registration.')\n        } else if (!error && data?.session) {\n          setLocalMessage('Account created and signed in successfully!')\n        }\n      }\n    } catch (err: unknown) {\n      setLocalError(err?.message || 'An unexpected error occurred')\n    }\n  }\n\n  const handleResendConfirmation = async () => {\n    if (!email.trim()) {\n      setLocalError('Please enter your email address')\n      return\n    }\n\n    try {\n      const { error } = await resendConfirmation(email)\n      if (!error) {\n        setLocalMessage('Confirmation email sent! Please check your inbox.')\n      }\n    } catch (err: unknown) {\n      setLocalError(err?.message || 'Failed to resend confirmation email')\n    }\n  }\n\n  const handleForgotPassword = async () => {\n    if (!email.trim()) {\n      setLocalError('Please enter your email address first')\n      return\n    }\n\n    try {\n      const { error } = await resetPassword(email)\n      if (!error) {\n        setLocalMessage('Password reset email sent! Please check your inbox.')\n      }\n    } catch (err: unknown) {\n      setLocalError(err?.message || 'Failed to send password reset email')\n    }\n  }\n\n  const displayError = localError || error\n  const displayMessage = localMessage\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader>\n        <CardTitle>\n          {mode === 'signin' ? 'Sign In' : 'Sign Up'}\n        </CardTitle>\n        <CardDescription>\n          {mode === 'signin'\n            ? 'Enter your credentials to access your timelines'\n            : 'Create an account to start building timelines'\n          }\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              disabled={loading}\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\">Password</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              disabled={loading}\n              minLength={6}\n              placeholder={mode === 'signup' ? 'At least 6 characters' : 'Enter your password'}\n            />\n          </div>\n\n          {displayError && (\n            <div className=\"text-sm text-red-600 bg-red-50 p-3 rounded-md flex items-start gap-2\">\n              <AlertCircle className=\"w-4 h-4 mt-0.5 flex-shrink-0\" />\n              <span>{displayError}</span>\n            </div>\n          )}\n\n          {displayMessage && (\n            <div className=\"text-sm text-green-600 bg-green-50 p-3 rounded-md flex items-start gap-2\">\n              <CheckCircle className=\"w-4 h-4 mt-0.5 flex-shrink-0\" />\n              <span>{displayMessage}</span>\n            </div>\n          )}\n\n          {isEmailConfirmationPending && (\n            <div className=\"text-sm text-blue-600 bg-blue-50 p-3 rounded-md\">\n              <p className=\"mb-2\">Waiting for email confirmation.</p>\n              <button\n                type=\"button\"\n                onClick={handleResendConfirmation}\n                className=\"text-blue-700 hover:text-blue-900 underline\"\n                disabled={loading}\n              >\n                Resend confirmation email\n              </button>\n            </div>\n          )}\n\n          <Button type=\"submit\" className=\"w-full\" disabled={loading}>\n            {loading ? 'Loading...' : (mode === 'signin' ? 'Sign In' : 'Sign Up')}\n          </Button>\n        </form>\n\n        <div className=\"mt-4 space-y-2 text-center\">\n          <button\n            type=\"button\"\n            onClick={() => onModeChange(mode === 'signin' ? 'signup' : 'signin')}\n            className=\"text-sm text-blue-600 hover:text-blue-800 underline\"\n            disabled={loading}\n          >\n            {mode === 'signin'\n              ? \"Don't have an account? Sign up\"\n              : 'Already have an account? Sign in'\n            }\n          </button>\n\n          {mode === 'signin' && (\n            <div>\n              <button\n                type=\"button\"\n                onClick={handleForgotPassword}\n                className=\"text-sm text-gray-600 hover:text-gray-800 underline\"\n                disabled={loading}\n              >\n                Forgot your password?\n              </button>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAeO,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,EACJ,MAAM,EACN,MAAM,EACN,aAAa,EACb,kBAAkB,EAClB,OAAO,EACP,KAAK,EACL,0BAA0B,EAC1B,UAAU,EACX,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEV,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,gBAAgB;QAChB;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,cAAc;YACd,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM;YACxB,cAAc;YACd,OAAO;QACT;QAEA,IAAI,CAAC,UAAU;YACb,cAAc;YACd,OAAO;QACT;QAEA,IAAI,SAAS,YAAY,SAAS,MAAM,GAAG,GAAG;YAC5C,cAAc;YACd,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,cAAc;QACd,gBAAgB;QAChB;QAEA,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,CAAC,OAAO;oBACV,gBAAgB;gBAClB;YACF,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBAC5C,IAAI,CAAC,SAAS,MAAM,QAAQ,CAAC,MAAM,SAAS;oBAC1C,gBAAgB;gBAClB,OAAO,IAAI,CAAC,SAAS,MAAM,SAAS;oBAClC,gBAAgB;gBAClB;YACF;QACF,EAAE,OAAO,KAAc;YACrB,cAAc,KAAK,WAAW;QAChC;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,cAAc;YACd;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAmB;YAC3C,IAAI,CAAC,OAAO;gBACV,gBAAgB;YAClB;QACF,EAAE,OAAO,KAAc;YACrB,cAAc,KAAK,WAAW;QAChC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,cAAc;YACd;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,cAAc;YACtC,IAAI,CAAC,OAAO;gBACV,gBAAgB;YAClB;QACF,EAAE,OAAO,KAAc;YACrB,cAAc,KAAK,WAAW;QAChC;IACF;IAEA,MAAM,eAAe,cAAc;IACnC,MAAM,iBAAiB;IAEvB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCACP,SAAS,WAAW,YAAY;;;;;;kCAEnC,8OAAC,gIAAA,CAAA,kBAAe;kCACb,SAAS,WACN,oDACA;;;;;;;;;;;;0BAIR,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,QAAQ;wCACR,UAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ;wCACR,UAAU;wCACV,WAAW;wCACX,aAAa,SAAS,WAAW,0BAA0B;;;;;;;;;;;;4BAI9D,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAM;;;;;;;;;;;;4BAIV,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAM;;;;;;;;;;;;4BAIV,4CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDACpB,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,UAAU;kDACX;;;;;;;;;;;;0CAML,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;gCAAS,UAAU;0CAChD,UAAU,eAAgB,SAAS,WAAW,YAAY;;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,aAAa,SAAS,WAAW,WAAW;gCAC3D,WAAU;gCACV,UAAU;0CAET,SAAS,WACN,mCACA;;;;;;4BAIL,SAAS,0BACR,8OAAC;0CACC,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-timelines.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabase } from '@/lib/supabase'\nimport { Timeline, CreateTimelineData, UpdateTimelineData } from '@/types/timeline'\n\n// Mock data storage\nconst getMockTimelines = (): Timeline[] => {\n  const stored = localStorage.getItem('mock-timelines')\n  return stored ? JSON.parse(stored) : []\n}\n\nconst setMockTimelines = (timelines: Timeline[]) => {\n  localStorage.setItem('mock-timelines', JSON.stringify(timelines))\n}\n\n// Fetch all timelines for the current user\nexport const useTimelines = () => {\n  return useQuery({\n    queryKey: ['timelines'],\n    queryFn: async (): Promise<Timeline[]> => {\n      if (!supabase) {\n        // Mock data for development\n        return getMockTimelines()\n      }\n\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .select('*')\n        .eq('user_id', user.id)\n        .order('updated_at', { ascending: false })\n\n      if (error) throw error\n      return data || []\n    },\n  })\n}\n\n// Fetch a single timeline by ID\nexport const useTimeline = (id: string) => {\n  return useQuery({\n    queryKey: ['timeline', id],\n    queryFn: async (): Promise<Timeline> => {\n      if (!supabase) {\n        // Mock data for development\n        const timelines = getMockTimelines()\n        const timeline = timelines.find(t => t.id === id)\n        if (!timeline) throw new Error('Timeline not found')\n        return timeline\n      }\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .select('*')\n        .eq('id', id)\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    enabled: !!id,\n  })\n}\n\n// Create a new timeline\nexport const useCreateTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (timelineData: CreateTimelineData): Promise<Timeline> => {\n      if (!supabase) {\n        // Mock data for development\n        const mockUser = localStorage.getItem('mock-user')\n        if (!mockUser) throw new Error('User not authenticated')\n\n        const user = JSON.parse(mockUser)\n        const newTimeline: Timeline = {\n          id: `timeline-${Date.now()}`,\n          title: timelineData.title,\n          description: timelineData.description || null,\n          user_id: user.id,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        }\n\n        const timelines = getMockTimelines()\n        timelines.push(newTimeline)\n        setMockTimelines(timelines)\n        return newTimeline\n      }\n\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) throw new Error('User not authenticated')\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .insert({\n          ...timelineData,\n          user_id: user.id,\n        })\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n    },\n  })\n}\n\n// Update a timeline\nexport const useUpdateTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineData }): Promise<Timeline> => {\n      if (!supabase) {\n        // Mock data for development\n        const timelines = getMockTimelines()\n        const timelineIndex = timelines.findIndex(t => t.id === id)\n        if (timelineIndex === -1) throw new Error('Timeline not found')\n\n        const updatedTimeline = {\n          ...timelines[timelineIndex],\n          ...updates,\n          updated_at: new Date().toISOString(),\n        }\n\n        timelines[timelineIndex] = updatedTimeline\n        setMockTimelines(timelines)\n        return updatedTimeline\n      }\n\n      const { data, error } = await supabase\n        .from('timelines')\n        .update(updates)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n      queryClient.invalidateQueries({ queryKey: ['timeline', data.id] })\n    },\n  })\n}\n\n// Delete a timeline\nexport const useDeleteTimeline = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (id: string): Promise<void> => {\n      if (!supabase) {\n        // Mock data for development\n        const timelines = getMockTimelines()\n        const filteredTimelines = timelines.filter(t => t.id !== id)\n        setMockTimelines(filteredTimelines)\n\n        // Also remove associated timeline items\n        const timelineItems = localStorage.getItem('mock-timeline-items')\n        if (timelineItems) {\n          const items = JSON.parse(timelineItems)\n          const filteredItems = items.filter((item: any) => item.timeline_id !== id)\n          localStorage.setItem('mock-timeline-items', JSON.stringify(filteredItems))\n        }\n        return\n      }\n\n      const { error } = await supabase\n        .from('timelines')\n        .delete()\n        .eq('id', id)\n\n      if (error) throw error\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['timelines'] })\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;AAGA,oBAAoB;AACpB,MAAM,mBAAmB;IACvB,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;AACzC;AAEA,MAAM,mBAAmB,CAAC;IACxB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;AACxD;AAGO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAY;QACvB,SAAS;YACP,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,OAAO;YACT;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB;IACF;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS;YACP,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,YAAY;gBAClB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;gBAC/B,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;gBAE/B,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,MAAM,cAAwB;oBAC5B,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;oBAC5B,OAAO,aAAa,KAAK;oBACzB,aAAa,aAAa,WAAW,IAAI;oBACzC,SAAS,KAAK,EAAE;oBAChB,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,MAAM,YAAY;gBAClB,UAAU,IAAI,CAAC;gBACf,iBAAiB;gBACjB,OAAO;YACT;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;gBACN,GAAG,YAAY;gBACf,SAAS,KAAK,EAAE;YAClB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;QAC1D;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,OAAO,EAA+C;YAC7E,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,YAAY;gBAClB,MAAM,gBAAgB,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACxD,IAAI,kBAAkB,CAAC,GAAG,MAAM,IAAI,MAAM;gBAE1C,MAAM,kBAAkB;oBACtB,GAAG,SAAS,CAAC,cAAc;oBAC3B,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,SAAS,CAAC,cAAc,GAAG;gBAC3B,iBAAiB;gBACjB,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;YACxD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY,KAAK,EAAE;iBAAC;YAAC;QAClE;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,YAAY;gBAClB,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzD,iBAAiB;gBAEjB,wCAAwC;gBACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,QAAQ,KAAK,KAAK,CAAC;oBACzB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,OAAc,KAAK,WAAW,KAAK;oBACvE,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;gBAC7D;gBACA;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;QACnB;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAY;YAAC;QAC1D;IACF;AACF", "debugId": null}}, {"offset": {"line": 1346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/timeline/timeline-list.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Plus, Calendar, Edit, Trash2, <PERSON><PERSON><PERSON>, <PERSON>, Users } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { useTimelines, useCreateTimeline, useDeleteTimeline } from '@/hooks/use-timelines'\nimport { Timeline } from '@/types/timeline'\n\ninterface TimelineListProps {\n  onTimelineSelect: (timeline: Timeline) => void\n}\n\nexport const TimelineList: React.FC<TimelineListProps> = ({ onTimelineSelect }) => {\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\n  const [newTimelineTitle, setNewTimelineTitle] = useState('')\n  const [newTimelineDescription, setNewTimelineDescription] = useState('')\n\n  const { data: timelines, isLoading, error } = useTimelines()\n  const createTimelineMutation = useCreateTimeline()\n  const deleteTimelineMutation = useDeleteTimeline()\n\n  const handleCreateTimeline = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!newTimelineTitle.trim()) return\n\n    try {\n      const newTimeline = await createTimelineMutation.mutateAsync({\n        title: newTimelineTitle.trim(),\n        description: newTimelineDescription.trim() || undefined,\n      })\n\n      setNewTimelineTitle('')\n      setNewTimelineDescription('')\n      setIsCreateDialogOpen(false)\n      onTimelineSelect(newTimeline)\n    } catch (error) {\n      console.error('Failed to create timeline:', error)\n    }\n  }\n\n  const handleDeleteTimeline = async (timelineId: string) => {\n    if (!confirm('Are you sure you want to delete this timeline? This action cannot be undone.')) {\n      return\n    }\n\n    try {\n      await deleteTimelineMutation.mutateAsync(timelineId)\n    } catch (error) {\n      console.error('Failed to delete timeline:', error)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">Loading timelines...</div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-red-500\">Failed to load timelines</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">My Timelines</h1>\n\n        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>\n              <Plus className=\"w-4 h-4 mr-2\" />\n              New Timeline\n            </Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Create New Timeline</DialogTitle>\n              <DialogDescription>\n                {!timelines || timelines.length === 0\n                  ? \"Let's create your first timeline! Give it a meaningful title and description to get started.\"\n                  : \"Create a new timeline to organize your events chronologically.\"\n                }\n              </DialogDescription>\n            </DialogHeader>\n            <form onSubmit={handleCreateTimeline} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"title\">Title</Label>\n                <Input\n                  id=\"title\"\n                  value={newTimelineTitle}\n                  onChange={(e) => setNewTimelineTitle(e.target.value)}\n                  placeholder={!timelines || timelines.length === 0\n                    ? \"e.g., My Life Journey, Project Milestones, Company History\"\n                    : \"Enter timeline title\"\n                  }\n                  required\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">Description (optional)</Label>\n                <Textarea\n                  id=\"description\"\n                  value={newTimelineDescription}\n                  onChange={(e) => setNewTimelineDescription(e.target.value)}\n                  placeholder={!timelines || timelines.length === 0\n                    ? \"Describe what this timeline will track - key events, milestones, or memories you want to organize\"\n                    : \"Enter timeline description\"\n                  }\n                  rows={3}\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsCreateDialogOpen(false)}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  disabled={createTimelineMutation.isPending}\n                >\n                  {createTimelineMutation.isPending\n                    ? 'Creating...'\n                    : (!timelines || timelines.length === 0\n                        ? 'Create My First Timeline'\n                        : 'Create Timeline'\n                      )\n                  }\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {!timelines || timelines.length === 0 ? (\n        <div className=\"space-y-8\">\n          {/* Welcome Header */}\n          <div className=\"text-center\">\n            <div className=\"flex justify-center mb-4\">\n              <div className=\"relative\">\n                <Calendar className=\"w-16 h-16 text-blue-500\" />\n                <Sparkles className=\"w-6 h-6 text-yellow-500 absolute -top-1 -right-1\" />\n              </div>\n            </div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome to Timeline App!</h2>\n            <p className=\"text-gray-600 max-w-md mx-auto\">\n              Create your first timeline to start organizing events, milestones, and memories in a beautiful visual format.\n            </p>\n          </div>\n\n          {/* Feature Cards */}\n          <div className=\"grid md:grid-cols-3 gap-4 max-w-4xl mx-auto\">\n            <Card className=\"text-center p-4\">\n              <CardContent className=\"pt-4\">\n                <Clock className=\"w-8 h-8 text-blue-500 mx-auto mb-3\" />\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Chronological Order</h3>\n                <p className=\"text-sm text-gray-600\">\n                  Events are automatically organized by date for clear storytelling.\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"text-center p-4\">\n              <CardContent className=\"pt-4\">\n                <Edit className=\"w-8 h-8 text-green-500 mx-auto mb-3\" />\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Interactive Canvas</h3>\n                <p className=\"text-sm text-gray-600\">\n                  Click anywhere to add events and drag them to perfect positions.\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"text-center p-4\">\n              <CardContent className=\"pt-4\">\n                <Users className=\"w-8 h-8 text-purple-500 mx-auto mb-3\" />\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Personal & Shareable</h3>\n                <p className=\"text-sm text-gray-600\">\n                  Create timelines for projects, life events, or historical research.\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Call to Action */}\n          <Card className=\"max-w-md mx-auto\">\n            <CardContent className=\"text-center py-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Ready to get started?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Create your first timeline and start adding events to see the magic happen.\n              </p>\n              <Button\n                onClick={() => setIsCreateDialogOpen(true)}\n                size=\"lg\"\n                className=\"w-full\"\n              >\n                <Plus className=\"w-5 h-5 mr-2\" />\n                Create Your First Timeline\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      ) : (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {timelines.map((timeline) => (\n            <Card key={timeline.id} className=\"hover:shadow-md transition-shadow cursor-pointer\">\n              <CardHeader>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\" onClick={() => onTimelineSelect(timeline)}>\n                    <CardTitle className=\"text-lg\">{timeline.title}</CardTitle>\n                    {timeline.description && (\n                      <CardDescription className=\"mt-2\">\n                        {timeline.description}\n                      </CardDescription>\n                    )}\n                  </div>\n\n                  <div className=\"flex space-x-1\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteTimeline(timeline.id)\n                      }}\n                      disabled={deleteTimelineMutation.isPending}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardHeader>\n\n              <CardContent onClick={() => onTimelineSelect(timeline)}>\n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                  <span>\n                    Updated {new Date(timeline.updated_at).toLocaleDateString()}\n                  </span>\n                  <Badge variant=\"secondary\">Timeline</Badge>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAkBO,MAAM,eAA4C,CAAC,EAAE,gBAAgB,EAAE;IAC5E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,EAAE,MAAM,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IACzD,MAAM,yBAAyB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAC/C,MAAM,yBAAyB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAE/C,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAEhB,IAAI,CAAC,iBAAiB,IAAI,IAAI;QAE9B,IAAI;YACF,MAAM,cAAc,MAAM,uBAAuB,WAAW,CAAC;gBAC3D,OAAO,iBAAiB,IAAI;gBAC5B,aAAa,uBAAuB,IAAI,MAAM;YAChD;YAEA,oBAAoB;YACpB,0BAA0B;YAC1B,sBAAsB;YACtB,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,iFAAiF;YAC5F;QACF;QAEA,IAAI;YACF,MAAM,uBAAuB,WAAW,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAe;;;;;;;;;;;IAGpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAEjD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAoB,cAAc;;0CAC9C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DACf,CAAC,aAAa,UAAU,MAAM,KAAK,IAChC,iGACA;;;;;;;;;;;;kDAIR,8OAAC;wCAAK,UAAU;wCAAsB,WAAU;;0DAC9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,aAAa,CAAC,aAAa,UAAU,MAAM,KAAK,IAC5C,+DACA;wDAEJ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;wDACzD,aAAa,CAAC,aAAa,UAAU,MAAM,KAAK,IAC5C,sGACA;wDAEJ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,sBAAsB;kEACtC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU,uBAAuB,SAAS;kEAEzC,uBAAuB,SAAS,GAC7B,gBACC,CAAC,aAAa,UAAU,MAAM,KAAK,IAChC,6BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUnB,CAAC,aAAa,UAAU,MAAM,KAAK,kBAClC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAMhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAMzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAMzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,sBAAsB;oCACrC,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;qCAOzC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAS,SAAS,IAAM,iBAAiB;;8DACtD,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,KAAK;;;;;;gDAC7C,SAAS,WAAW,kBACnB,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,SAAS,WAAW;;;;;;;;;;;;sDAK3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,qBAAqB,SAAS,EAAE;gDAClC;gDACA,UAAU,uBAAuB,SAAS;0DAE1C,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,SAAS,IAAM,iBAAiB;0CAC3C,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDACK,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;sDAE3D,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;;;;;;;;;;;;uBAjCtB,SAAS,EAAE;;;;;;;;;;;;;;;;AA0ClC", "debugId": null}}, {"offset": {"line": 1993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/components/timeline/timeline-canvas.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Calendar } from 'lucide-react'\nimport { TimelineItem } from '@/types/timeline'\n\ninterface TimelineCanvasProps {\n  timelineItems: TimelineItem[]\n  onItemClick?: (item: TimelineItem) => void\n  onItemMove?: (itemId: string, x: number, y: number) => void\n  onCanvasClick?: (x: number, y: number) => void\n}\n\nexport const TimelineCanvas: React.FC<TimelineCanvasProps> = ({\n  timelineItems,\n  onItemClick,\n  onCanvasClick,\n}) => {\n  const handleCanvasClick = (e: React.MouseEvent<HTMLDivElement>) => {\n    const rect = e.currentTarget.getBoundingClientRect()\n    const x = e.clientX - rect.left\n    const y = e.clientY - rect.top\n    onCanvasClick?.(x, y)\n  }\n\n  const handleItemClick = (item: TimelineItem, e: React.MouseEvent) => {\n    e.stopPropagation()\n    onItemClick?.(item)\n  }\n\n  return (\n    <div\n      className=\"w-full h-full bg-gray-50 relative overflow-hidden cursor-crosshair\"\n      onClick={handleCanvasClick}\n    >\n      {/* Grid background */}\n      <div\n        className=\"absolute inset-0 opacity-20\"\n        style={{\n          backgroundImage: 'linear-gradient(to right, #e5e7eb 1px, transparent 1px), linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)',\n          backgroundSize: '20px 20px'\n        }}\n      />\n\n      {/* Timeline items */}\n      {timelineItems.map((item) => (\n        <div\n          key={item.id}\n          className=\"absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group\"\n          style={{\n            left: item.x_position,\n            top: item.y_position,\n          }}\n          onClick={(e) => handleItemClick(item, e)}\n        >\n          {/* Item circle */}\n          <div className=\"w-4 h-4 bg-blue-500 rounded-full border-2 border-blue-700 group-hover:w-5 group-hover:h-5 transition-all duration-200\" />\n\n          {/* Item title */}\n          <div className=\"absolute top-6 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 whitespace-nowrap bg-white px-2 py-1 rounded shadow-sm\">\n            {item.title}\n          </div>\n\n          {/* Item date */}\n          <div className=\"absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap\">\n            {new Date(item.date).toLocaleDateString()}\n          </div>\n        </div>\n      ))}\n\n      {/* Timeline line connecting items */}\n      {timelineItems.length > 1 && (\n        <svg className=\"absolute inset-0 pointer-events-none\">\n          <path\n            d={timelineItems\n              .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n              .map((item, index) =>\n                `${index === 0 ? 'M' : 'L'} ${item.x_position} ${item.y_position}`\n              )\n              .join(' ')\n            }\n            stroke=\"#d1d5db\"\n            strokeWidth=\"2\"\n            fill=\"none\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          />\n        </svg>\n      )}\n\n      {/* Instructions */}\n      {timelineItems.length === 0 && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-gray-500 max-w-md mx-auto px-4\">\n            <div className=\"mb-4\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Calendar className=\"w-8 h-8 text-blue-500\" />\n              </div>\n            </div>\n            <div className=\"text-xl font-semibold text-gray-700 mb-3\">Your timeline is ready!</div>\n            <div className=\"text-gray-600 mb-4\">\n              Click anywhere on this canvas to add your first event. Each event will be positioned chronologically and connected with a beautiful timeline.\n            </div>\n            <div className=\"text-sm text-gray-500 bg-gray-50 rounded-lg p-3\">\n              💡 <strong>Tip:</strong> Events are automatically sorted by date, so don&apos;t worry about perfect positioning - just click and create!\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaO,MAAM,iBAAgD,CAAC,EAC5D,aAAa,EACb,WAAW,EACX,aAAa,EACd;IACC,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;QAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;QAC9B,gBAAgB,GAAG;IACrB;IAEA,MAAM,kBAAkB,CAAC,MAAoB;QAC3C,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;;0BAGT,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;YAID,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,MAAM,KAAK,UAAU;wBACrB,KAAK,KAAK,UAAU;oBACtB;oBACA,SAAS,CAAC,IAAM,gBAAgB,MAAM;;sCAGtC,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACZ,KAAK,KAAK;;;;;;sCAIb,8OAAC;4BAAI,WAAU;sCACZ,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB;;;;;;;mBAlBpC,KAAK,EAAE;;;;;YAwBf,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,GAAG,cACA,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,GAAG,CAAC,CAAC,MAAM,QACV,GAAG,UAAU,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,UAAU,EAAE,EAEnE,IAAI,CAAC;oBAER,QAAO;oBACP,aAAY;oBACZ,MAAK;oBACL,eAAc;oBACd,gBAAe;;;;;;;;;;;YAMpB,cAAc,MAAM,KAAK,mBACxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGxB,8OAAC;4BAAI,WAAU;sCAA2C;;;;;;sCAC1D,8OAAC;4BAAI,WAAU;sCAAqB;;;;;;sCAGpC,8OAAC;4BAAI,WAAU;;gCAAkD;8CAC5D,8OAAC;8CAAO;;;;;;gCAAa;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/hooks/use-timeline-items.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabase } from '@/lib/supabase'\nimport { TimelineItem, CreateTimelineItemData, UpdateTimelineItemData } from '@/types/timeline'\n\n// Mock data storage\nconst getMockTimelineItems = (): TimelineItem[] => {\n  const stored = localStorage.getItem('mock-timeline-items')\n  return stored ? JSON.parse(stored) : []\n}\n\nconst setMockTimelineItems = (items: TimelineItem[]) => {\n  localStorage.setItem('mock-timeline-items', JSON.stringify(items))\n}\n\n// Fetch all items for a specific timeline\nexport const useTimelineItems = (timelineId: string) => {\n  return useQuery({\n    queryKey: ['timeline-items', timelineId],\n    queryFn: async (): Promise<TimelineItem[]> => {\n      if (!supabase) {\n        // Mock data for development\n        const allItems = getMockTimelineItems()\n        return allItems.filter(item => item.timeline_id === timelineId)\n          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n      }\n\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .select('*')\n        .eq('timeline_id', timelineId)\n        .order('date', { ascending: true })\n\n      if (error) throw error\n      return data || []\n    },\n    enabled: !!timelineId,\n  })\n}\n\n// Create a new timeline item\nexport const useCreateTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (itemData: CreateTimelineItemData): Promise<TimelineItem> => {\n      if (!supabase) {\n        // Mock data for development\n        const newItem: TimelineItem = {\n          id: `item-${Date.now()}`,\n          ...itemData,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        }\n\n        const items = getMockTimelineItems()\n        items.push(newItem)\n        setMockTimelineItems(items)\n        return newItem\n      }\n\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .insert(itemData)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n\n// Update a timeline item\nexport const useUpdateTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTimelineItemData }): Promise<TimelineItem> => {\n      if (!supabase) {\n        // Mock data for development\n        const items = getMockTimelineItems()\n        const itemIndex = items.findIndex(item => item.id === id)\n        if (itemIndex === -1) throw new Error('Timeline item not found')\n\n        const updatedItem = {\n          ...items[itemIndex],\n          ...updates,\n          updated_at: new Date().toISOString(),\n        }\n\n        items[itemIndex] = updatedItem\n        setMockTimelineItems(items)\n        return updatedItem\n      }\n\n      const { data, error } = await supabase\n        .from('timeline_items')\n        .update(updates)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) throw error\n      return data\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n\n// Delete a timeline item\nexport const useDeleteTimelineItem = () => {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async (id: string): Promise<{ timeline_id: string }> => {\n      if (!supabase) {\n        // Mock data for development\n        const items = getMockTimelineItems()\n        const item = items.find(item => item.id === id)\n        if (!item) throw new Error('Timeline item not found')\n\n        const filteredItems = items.filter(item => item.id !== id)\n        setMockTimelineItems(filteredItems)\n        return { timeline_id: item.timeline_id }\n      }\n\n      // First get the timeline_id before deleting\n      const { data: item, error: fetchError } = await supabase\n        .from('timeline_items')\n        .select('timeline_id')\n        .eq('id', id)\n        .single()\n\n      if (fetchError) throw fetchError\n\n      const { error } = await supabase\n        .from('timeline_items')\n        .delete()\n        .eq('id', id)\n\n      if (error) throw error\n      return { timeline_id: item.timeline_id }\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: ['timeline-items', data.timeline_id] })\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;;;AAGA,oBAAoB;AACpB,MAAM,uBAAuB;IAC3B,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;AACzC;AAEA,MAAM,uBAAuB,CAAC;IAC5B,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC7D;AAGO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAW;QACxC,SAAS;YACP,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,WAAW;gBACjB,OAAO,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,YACjD,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;YACzE;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAK;YAEnC,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,UAAwB;oBAC5B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBACxB,GAAG,QAAQ;oBACX,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,MAAM,QAAQ;gBACd,MAAM,IAAI,CAAC;gBACX,qBAAqB;gBACrB,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,OAAO,EAAmD;YACjF,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,QAAQ;gBACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACtD,IAAI,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM;gBAEtC,MAAM,cAAc;oBAClB,GAAG,KAAK,CAAC,UAAU;oBACnB,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,KAAK,CAAC,UAAU,GAAG;gBACnB,qBAAqB;gBACrB,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,4BAA4B;gBAC5B,MAAM,QAAQ;gBACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAC5C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACvD,qBAAqB;gBACrB,OAAO;oBAAE,aAAa,KAAK,WAAW;gBAAC;YACzC;YAEA,4CAA4C;YAC5C,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrD,IAAI,CAAC,kBACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,YAAY,MAAM;YAEtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,OAAO;gBAAE,aAAa,KAAK,WAAW;YAAC;QACzC;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB,KAAK,WAAW;iBAAC;YAAC;QACjF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documente/Timeline/timeline-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { AuthForm } from '@/components/auth/auth-form'\nimport { TimelineList } from '@/components/timeline/timeline-list'\nimport { TimelineCanvas } from '@/components/timeline/timeline-canvas'\nimport { useAuth } from '@/hooks/use-auth'\nimport { useTimelineItems, useCreateTimelineItem, useUpdateTimelineItem } from '@/hooks/use-timeline-items'\nimport { Timeline, TimelineItem } from '@/types/timeline'\nimport { Button } from '@/components/ui/button'\nimport { LogOut, ArrowLeft } from 'lucide-react'\n\nexport default function Home() {\n  const { user, loading, signOut } = useAuth()\n  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')\n  const [selectedTimeline, setSelectedTimeline] = useState<Timeline | null>(null)\n  const [selectedItem, setSelectedItem] = useState<TimelineItem | null>(null)\n\n  const { data: timelineItems = [], refetch } = useTimelineItems(selectedTimeline?.id || '')\n  const createItemMutation = useCreateTimelineItem()\n  const updateItemMutation = useUpdateTimelineItem()\n\n  const handleSignOut = async () => {\n    await signOut()\n    setSelectedTimeline(null)\n    setSelectedItem(null)\n  }\n\n  const handleTimelineSelect = (timeline: Timeline) => {\n    setSelectedTimeline(timeline)\n  }\n\n  const handleBackToTimelines = () => {\n    setSelectedTimeline(null)\n    setSelectedItem(null)\n  }\n\n  const handleCanvasClick = async (x: number, y: number) => {\n    if (!selectedTimeline) return\n\n    const newItem = {\n      timeline_id: selectedTimeline.id,\n      title: 'New Event',\n      description: 'Click to edit',\n      date: new Date().toISOString(),\n      x_position: x,\n      y_position: y,\n    }\n\n    try {\n      await createItemMutation.mutateAsync(newItem)\n      refetch()\n    } catch (error) {\n      console.error('Failed to create timeline item:', error)\n    }\n  }\n\n  const handleItemMove = async (itemId: string, x: number, y: number) => {\n    try {\n      await updateItemMutation.mutateAsync({\n        id: itemId,\n        updates: { x_position: x, y_position: y }\n      })\n      refetch()\n    } catch (error) {\n      console.error('Failed to update timeline item:', error)\n    }\n  }\n\n  const handleItemClick = (item: TimelineItem) => {\n    setSelectedItem(item)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg text-gray-600\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <AuthForm mode={authMode} onModeChange={setAuthMode} />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-4\">\n              {selectedTimeline && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleBackToTimelines}\n                >\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back to Timelines\n                </Button>\n              )}\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                {selectedTimeline ? selectedTimeline.title : 'Timeline App'}\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">{user.email}</span>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleSignOut}>\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                Sign Out\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!selectedTimeline ? (\n          <TimelineList onTimelineSelect={handleTimelineSelect} />\n        ) : (\n          <div className=\"h-[calc(100vh-200px)] bg-white rounded-lg shadow-sm border\">\n            <TimelineCanvas\n              timelineItems={timelineItems}\n              onItemClick={handleItemClick}\n              onItemMove={handleItemMove}\n              onCanvasClick={handleCanvasClick}\n            />\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEtE,MAAM,EAAE,MAAM,gBAAgB,EAAE,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,MAAM;IACvF,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAC/C,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAE/C,MAAM,gBAAgB;QACpB,MAAM;QACN,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,OAAO,GAAW;QAC1C,IAAI,CAAC,kBAAkB;QAEvB,MAAM,UAAU;YACd,aAAa,iBAAiB,EAAE;YAChC,OAAO;YACP,aAAa;YACb,MAAM,IAAI,OAAO,WAAW;YAC5B,YAAY;YACZ,YAAY;QACd;QAEA,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB,OAAO,QAAgB,GAAW;QACvD,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBACnC,IAAI;gBACJ,SAAS;oBAAE,YAAY;oBAAG,YAAY;gBAAE;YAC1C;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAU,cAAc;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,kCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI1C,8OAAC;wCAAG,WAAU;kDACX,mBAAmB,iBAAiB,KAAK,GAAG;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAyB,KAAK,KAAK;;;;;;kDACnD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAK,WAAU;0BACb,CAAC,iCACA,8OAAC,kJAAA,CAAA,eAAY;oBAAC,kBAAkB;;;;;yCAEhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oJAAA,CAAA,iBAAc;wBACb,eAAe;wBACf,aAAa;wBACb,YAAY;wBACZ,eAAe;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}