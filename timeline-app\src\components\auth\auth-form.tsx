'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { AlertCircle, CheckCircle } from 'lucide-react'

interface AuthFormProps {
  mode: 'signin' | 'signup'
  onModeChange: (mode: 'signin' | 'signup') => void
}

export const AuthForm: React.FC<AuthFormProps> = ({ mode, onModeChange }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const [localError, setLocalError] = useState<string | null>(null)
  const [localMessage, setLocalMessage] = useState<string | null>(null)

  const {
    signIn,
    signUp,
    resetPassword,
    resendConfirmation,
    loading,
    error,
    isEmailConfirmationPending,
    clearError
  } = useAuth()

  // Clear local messages when mode changes
  useEffect(() => {
    setLocalError(null)
    setLocalMessage(null)
    clearError()
  }, [mode, clearError])

  const validateForm = () => {
    if (!email.trim()) {
      setLocalError('Email is required')
      return false
    }

    if (!email.includes('@')) {
      setLocalError('Please enter a valid email address')
      return false
    }

    if (!password) {
      setLocalError('Password is required')
      return false
    }

    if (mode === 'signup' && password.length < 6) {
      setLocalError('Password must be at least 6 characters long')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLocalError(null)
    setLocalMessage(null)
    clearError()

    if (!validateForm()) return

    try {
      if (mode === 'signin') {
        const { error } = await signIn(email, password)
        if (!error) {
          setLocalMessage('Successfully signed in!')
        }
      } else {
        const { data, error } = await signUp(email, password)
        if (!error && data?.user && !data?.session) {
          setLocalMessage('Please check your email and click the confirmation link to complete your registration.')
        } else if (!error && data?.session) {
          setLocalMessage('Account created and signed in successfully!')
        }
      }
    } catch (err: unknown) {
      setLocalError(err?.message || 'An unexpected error occurred')
    }
  }

  const handleResendConfirmation = async () => {
    if (!email.trim()) {
      setLocalError('Please enter your email address')
      return
    }

    try {
      const { error } = await resendConfirmation(email)
      if (!error) {
        setLocalMessage('Confirmation email sent! Please check your inbox.')
      }
    } catch (err: unknown) {
      setLocalError(err?.message || 'Failed to resend confirmation email')
    }
  }

  const handleForgotPassword = async () => {
    if (!email.trim()) {
      setLocalError('Please enter your email address first')
      return
    }

    try {
      const { error } = await resetPassword(email)
      if (!error) {
        setLocalMessage('Password reset email sent! Please check your inbox.')
      }
    } catch (err: unknown) {
      setLocalError(err?.message || 'Failed to send password reset email')
    }
  }

  const displayError = localError || error
  const displayMessage = localMessage

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>
          {mode === 'signin' ? 'Sign In' : 'Sign Up'}
        </CardTitle>
        <CardDescription>
          {mode === 'signin'
            ? 'Enter your credentials to access your timelines'
            : 'Create an account to start building timelines'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
              placeholder="Enter your email"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              minLength={6}
              placeholder={mode === 'signup' ? 'At least 6 characters' : 'Enter your password'}
            />
          </div>

          {displayError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md flex items-start gap-2">
              <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span>{displayError}</span>
            </div>
          )}

          {displayMessage && (
            <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md flex items-start gap-2">
              <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span>{displayMessage}</span>
            </div>
          )}

          {isEmailConfirmationPending && (
            <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
              <p className="mb-2">Waiting for email confirmation.</p>
              <button
                type="button"
                onClick={handleResendConfirmation}
                className="text-blue-700 hover:text-blue-900 underline"
                disabled={loading}
              >
                Resend confirmation email
              </button>
            </div>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Loading...' : (mode === 'signin' ? 'Sign In' : 'Sign Up')}
          </Button>
        </form>

        <div className="mt-4 space-y-2 text-center">
          <button
            type="button"
            onClick={() => onModeChange(mode === 'signin' ? 'signup' : 'signin')}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
            disabled={loading}
          >
            {mode === 'signin'
              ? "Don't have an account? Sign up"
              : 'Already have an account? Sign in'
            }
          </button>

          {mode === 'signin' && (
            <div>
              <button
                type="button"
                onClick={handleForgotPassword}
                className="text-sm text-gray-600 hover:text-gray-800 underline"
                disabled={loading}
              >
                Forgot your password?
              </button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
