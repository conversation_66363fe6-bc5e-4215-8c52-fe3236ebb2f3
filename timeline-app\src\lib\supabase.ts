import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if we have valid credentials
const hasValidCredentials = supabaseUrl && supabaseAnonKey &&
  !supabaseUrl.includes('placeholder') &&
  !supabaseAnonKey.includes('placeholder')

// Create Supabase client with proper configuration for password authentication
export const supabase = hasValidCredentials
  ? createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        // Use implicit flow for password authentication instead of PKCE
        flowType: 'implicit'
      }
    })
  : null

export type Database = {
  public: {
    Tables: {
      timelines: {
        Row: {
          id: string
          title: string
          description: string | null
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          user_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          user_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      timeline_items: {
        Row: {
          id: string
          timeline_id: string
          title: string
          description: string | null
          date: string
          x_position: number
          y_position: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          timeline_id: string
          title: string
          description?: string | null
          date: string
          x_position: number
          y_position: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          timeline_id?: string
          title?: string
          description?: string | null
          date?: string
          x_position?: number
          y_position?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
